<?php

namespace App\Http\Controllers;

use App\Models\Ad;
use App\Models\Banner;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Storage;
use Ya<PERSON>ra\DataTables\Facades\DataTables;

class BannerController extends Controller
{

    //
    public function index()
    {
        return view('admin.banners.list');
    }
    public function add()
    {
        return view('admin.banners.add');
    }
    //
    // Store method to save ad category data
    public function store(Request $request)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'link'          => 'required|url',
            'image'         => 'required|image|mimes:jpg,jpeg,png,gif|max:2048|dimensions:width=2000,height=500', // Required image upload with exact dimensions
        ], [
            'image.required' => 'Banner image is required.',
            'image.dimensions' => 'Banner image must be exactly 2000 x 500 pixels. Please resize your image and try again.',
            'image.image' => 'Banner file must be a valid image.',
            'image.mimes' => 'Banner image must be a JPG, JPEG, PNG, or GIF file.',
            'image.max' => 'Banner image size must not exceed 2MB.',
        ]);
        $data = $request->all();
        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/banners/
            $filePath = $file->storeAs('banners', $filename, 'public');

            // Save the file path in the database
            $data['image'] = $filePath;
        }

        // Save data to database
        Banner::create($data);

        // Redirect to ad list page with success message
        return redirect()->route('banners.index')->with('success', $data['name'] . ' created successfully!');
    }
    public function edit($id)
    {
        $banner = Banner::where('id', $id)->first();


        return view('admin.banners.edit', compact('banner'));
    }
    public function update(Request $request, Banner $banner)
    {
        // Validate form data
        $validated = $request->validate([
            'name'          => 'required|string|max:255',
            'status'        => 'required',
            'link'          => 'required|url',
            'image'         => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048|dimensions:width=2000,height=500', // Optional image upload with exact dimensions
        ], [
            'image.dimensions' => 'Banner image must be exactly 2000 x 500 pixels. Please resize your image and try again.',
            'image.image' => 'Banner file must be a valid image.',
            'image.mimes' => 'Banner image must be a JPG, JPEG, PNG, or GIF file.',
            'image.max' => 'Banner image size must not exceed 2MB.',
        ]);

        $data = $request->except(['image']); // Exclude logo from mass update initially

        // Handle file upload
        if ($request->hasFile('image')) {
            $file = $request->file('image');

            // Generate a unique name using timestamp and original extension
            $filename = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();

            // Store the file in storage/app/public/banners/
            $filePath = $file->storeAs('banners', $filename, 'public');

            // Delete the old logo if exists
            if ($banner->image && Storage::disk('public')->exists($banner->image)) {
                Storage::disk('public')->delete($banner->image);
            }

            // Save the new file path
            $data['image'] = $filePath;
        }
        // Update vendorcategory data
        $banner->update($data);

        // Redirect to vendorcategory list page with success message
        return redirect()->route('banners.index')->with('success', $banner->name . ' updated successfully!');
    }

    public function getbanners(Request $request)
    {
        if ($request->ajax()) {
             // Get the logged-in user
            $user = Auth::user();
            // Fetch base vendor categories query
            $banners = Banner::select(['id', 'name', 'image', 'status', 'created_at']);



            return DataTables::of($banners)
                ->addColumn('status', function ($row) {
                    $checked = $row->status == 1 ? 'checked' : '';
                    $statusText = $row->status == 1 ? 'Active' : 'Inactive';

                    return '
                        <div class="form-switch switch-success d-flex align-items-center gap-2">
                            <input class="form-check-input status-toggle" type="checkbox" role="switch"
                                   data-id="' . $row->id . '" ' . $checked . '>
                            <label class="form-check-label fw-medium text-secondary-light mb-0">' . $statusText . '</label>
                        </div>';
                })
                ->addColumn('image', function ($row) {
                    if ($row->image) {
                        $imageUrl = url('storage/'.$row->image); // update path if needed
                        return '<img src="' . $imageUrl . '" alt="' . $row->name . '"  height="100" class="rounded">';
                    } else {
                        return '<img src="' . asset('assets/images/default.png') . '" width="50" height="50" class="rounded">';
                    }
                })

                ->addColumn('action', function ($row) {
                    $user = auth()->user(); // Get authenticated user
                    $output = '';
                    // Check 'vendor ad-edit' permission
                    if ($user->can('banner-edit')) {
                        $output .= '
                            <a href="' . route('banners.edit', $row->id) . '" class="w-32-px h-32-px bg-success-focus text-success-main rounded-circle d-inline-flex align-items-center justify-content-center">
                                <iconify-icon icon="lucide:edit"></iconify-icon>
                            </a>';
                    }

                    // Add delete button
                    $output .= '<button class="btn btn-danger btn-sm delete-banner" data-id="'. $row->id .'" title="Delete">
                                    <iconify-icon icon="lucide:trash-2"></iconify-icon>
                                </button>';

                    return $output;
                })
                ->filter(function ($query) use ($request) {

                    if ($request->has('searchkey') && $request->searchkey != '') {
                        $query->where(function ($q) use ($request) {
                            $q->where('name', 'like', "%{$request->searchkey}%")
                                ;
                        });
                    }
                })

                ->rawColumns(['image', 'status', 'action']) // Ensure HTML rendering
                ->make(true);
        }
    }

    /**
     * Update the status of a banner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateStatus(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'banner_id' => 'required|exists:banners,id',
            'status' => 'required|in:0,1',
        ]);

        try {
            // Find the banner
            $banner = Banner::findOrFail($request->banner_id);

            // Update status
            $banner->status = $request->status;
            $banner->save();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Banner status updated successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to update banner status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a banner.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request)
    {
        // Validate request
        $validated = $request->validate([
            'banner_id' => 'required|exists:banners,id',
        ]);

        try {
            // Find the banner
            $banner = Banner::findOrFail($request->banner_id);

            // Delete the banner image if exists
            if ($banner->image && Storage::disk('public')->exists($banner->image)) {
                Storage::disk('public')->delete($banner->image);
            }

            // Delete the banner
            $banner->delete();

            // Return success response
            return response()->json([
                'success' => true,
                'message' => 'Banner deleted successfully'
            ]);
        } catch (\Exception $e) {
            // Return error response
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete banner: ' . $e->getMessage()
            ], 500);
        }
    }
}
