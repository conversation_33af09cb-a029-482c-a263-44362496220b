@extends('frontend.layouts.master')
@section('content')

    <!-- ============================ Hero Banner  Start================================== -->
    <section class="bg-title call-to-act-wrap pt-4 pb-4">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">
                <a href="{{ $ads[18]->link }}" target="_blank">
                            <img src="{{ url('storage/' . $ads[18]->image) }}" class="img-fluid" alt="">
                        </a>
            </div>
        </div>

	</div>
</section>
    <div class="featured_slick_gallery gray">
        <div class="featured_slick_gallery-slide">
           @if ($property && $property->images && count($property->images))
                @foreach ($property->images as $image)
                    <div class="featured_slick_padd">
                        <a href="{{ url('storage/' . $image->image_path) }}" class="mfp-gallery">
                            <img src="{{ url('storage/' . $image->image_path) }}" class="img-fluid mx-auto" alt="" />
                        </a>
                    </div>
                @endforeach

            @endif


        </div>
    </div>
    <!-- ============================ Hero Banner End ================================== -->

    <!-- ============================ Property Detail Start ================================== -->
    <section class="gray-simple">
        <div class="container">
            <div class="row">

                <!-- property main detail -->
                <div class="col-lg-8 col-md-12 col-sm-12">

                    <div class="property_block_wrap style-2 p-4">
                        <div class="prt-detail-title-desc">
                            <span class="label text-light bg-success">
                                {{ $property->property_type == 1 ? 'For Sale' : 'For Rent' }}
                            </span>
                            @if($property->verified == 1)
                            <span class="label text-light bg-primary ms-2">
                                <span class="svg-icon text-light svg-icon-2hx me-1">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.3" d="M20.5543 4.37824L12.1798 2.02473C12.0626 1.99176 11.9376 1.99176 11.8203 2.02473L3.44572 4.37824C3.18118 4.45258 3 4.6807 3 4.93945V13.569C3 14.6914 3.48509 15.8404 4.4417 16.984C5.17231 17.8575 6.18314 18.7345 7.446 19.5909C9.56752 21.0295 11.6566 21.912 11.7445 21.9488C11.8258 21.9829 11.9129 22 12.0001 22C12.0872 22 12.1744 21.983 12.2557 21.9488C12.3435 21.912 14.4326 21.0295 16.5541 19.5909C17.8169 18.7345 18.8277 17.8575 19.5584 16.984C20.515 15.8404 21 14.6914 21 13.569V4.93945C21 4.6807 20.8189 4.45258 20.5543 4.37824Z" fill="currentColor"></path>
                                        <path d="M10.5606 11.3042L9.57283 10.3018C9.28174 10.0065 8.80522 10.0065 8.51412 10.3018C8.22897 10.5912 8.22897 11.0559 8.51412 11.3452L10.4182 13.2773C10.8099 13.6747 11.451 13.6747 11.8427 13.2773L15.4859 9.58051C15.771 9.29117 15.771 8.82648 15.4859 8.53714C15.1948 8.24176 14.7183 8.24176 14.4272 8.53714L11.7002 11.3042C11.3869 11.6221 10.874 11.6221 10.5606 11.3042Z" fill="currentColor"></path>
                                    </svg>
                                </span>Verified
                            </span>
                            @endif
                            <h3 class="mt-3">
                                {{ $property->title }}
                            </h3>
                            <span class="text-black"><i class="lni-map-marker"></i>
                                {{ $property->address }},{{ $property->area->name }}
                                {{ (($property->nearest_landmark )) ? ',Near '.$property->nearest_landmark : '' }},{{ (($property->distance )) ? ', '.$property->distance : '' }} {{$property->district->name}},{{$property->state->name}}-{{ $property->area->pincode }}
                            </span>
                            <h3 class="prt-price-fix text-primary mt-2">
                                @if($property->property_type == 1)
                                     ₹{{ $property->property_cost }}
                                @else
                                    ₹{{ $property->rent }}<sub>/month</sub>
                                @endif

                            </h3>
                            <div class="list-fx-features text-black">
                                <div class="listing-card-info-icon">
                                    <div class="inc-fleat-icon me-1"><img src="/frontend/assets/img/bed.svg"
                                            width="13" alt=""></div>{{ $property->no_of_bed_rooms }} BHK
                                </div>
                                <div class="listing-card-info-icon">
                                    <div class="inc-fleat-icon me-1"><img src="/frontend/assets/img/bathtub.svg"
                                            width="13" alt=""></div>{{ $property->no_of_bath_rooms }} Bath
                                </div>
                                <div class="listing-card-info-icon">
                                    <div class="inc-fleat-icon me-1"><img src="/frontend/assets/img/move.svg"
                                            width="13" alt=""></div>{{ $property->area_size }}{{ $property->areaType->name ?? 'SquareFeet' }}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- single-propertys-1 code  -->
                    <!-- Single Block Wrap -->
                    <div class="property_block_wrap style-2">

                        <div class="property_block_wrap_header">
                            <a data-bs-toggle="collapse" data-parent="#features" data-bs-target="#clOne"
                                aria-controls="clOne" href="javascript:void(0);" aria-expanded="false">
                                <h4 class="property_block_title">Detail & Features</h4>
                            </a>
                        </div>
                        <div id="clOne" class="panel-collapse collapse show" aria-labelledby="clOne">
                            <div class="block-body text-black">
                                <ul class="deatil_features text-black">
                                    <li class="text-black"><strong>Bedrooms:</strong> {{ $property->no_of_bed_rooms }} Beds</li>
                                    <li class="text-black"><strong>Bathrooms:</strong> {{ $property->no_of_bath_rooms }} Bath</li>
                                    <li class="text-black"><strong>Area Size:</strong> {{ $property->area_size }}{{ $property->areaType->name ?? 'SquareFeet' }}</li>
                                    <li class="text-black"><strong>Balconies:</strong> {{ $property->no_of_balconies }}</li>
                                    <li class="text-black"><strong>Building Type:</strong> {{$property->buildingType->type }}</li>
                                    <li class="text-black"><strong>Build Year:</strong> {{ $property->build_year }}</li>
                                    <li class="text-black"><strong>Car Parkings:</strong> {{ $property->no_of_car_parkings }}</li>
                                    <li class="text-black"><strong>Floors:</strong> {{ $property->no_of_floors }}</li>
                                    <li class="text-black"><strong>Facing:</strong> {{ $property->facing_name }}</li>

                                </ul>
                            </div>
                        </div>

                    </div>

                    <!-- Single Block Wrap -->
                    <div class="property_block_wrap style-2">

                        <div class="property_block_wrap_header">
                            <a data-bs-toggle="collapse" data-parent="#amen" data-bs-target="#clThree"
                                aria-controls="clThree" href="javascript:void(0);" aria-expanded="true">
                                <h4 class="property_block_title">Ameneties</h4>
                            </a>
                        </div>

                        <div id="clThree" class="panel-collapse collapse show">
                            <div class="block-body text-black">
                                <ul class="avl-features third color">
                                    @if($property->amenities_parking == 1)
                                    <li>Carparking</li>
                                    @endif
                                    @if($property->amenities_swimming_pool == 1)
                                    <li>Swimming Pool</li>
                                    @endif
                                    @if($property->amenities_lift == 1)
                                    <li>Lift</li>
                                    @endif
                                    @if($property->amenities_gym == 1)
                                    <li>Gym</li>
                                    @endif
                                    @if($property->amenities_spa == 1)
                                    <li>Spa</li>
                                    @endif
                                    @if($property->amenities_power_backup == 1)
                                    <li>Power Backup</li>
                                    @endif
                                    @if($property->amenities_water_supply == 1)
                                    <li>24x7 Water Supply</li>
                                    @endif
                                    @if($property->amenities_security == 1)
                                    <li>Security/Watchman</li>
                                    @endif
                                    @if($property->amenities_cctv == 1)
                                    <li>CCTV Surveillance</li>
                                    @endif
                                    @if($property->amenities_clubhouse == 1)
                                    <li>Clubhouse</li>
                                    @endif
                                    @if($property->amenities_indoor_games == 1)
                                    <li>Indoor Games Room</li>
                                    @endif
                                    @if($property->amenities_outdoor_games == 1)
                                    <li>Outdoor Sports</li>
                                    @endif
                                    @if($property->amenities_kids_play == 1)
                                    <li>Kids Play Area</li>
                                    @endif

                                </ul>

                                @if($property->other_amenities)
                                <div class="mt-4">
                                    <h6 class="text-primary mb-2">Other Amenities:</h6>
                                    <p class="text-muted text-black">{{ $property->other_amenities }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <!-- Single Block Wrap -->
                    <div class="property_block_wrap style-2">

                        <div class="property_block_wrap_header">
                            <a data-bs-toggle="collapse" data-parent="#features" data-bs-target="#clOne"
                                aria-controls="clOne" href="javascript:void(0);" aria-expanded="false">
                                <h4 class="property_block_title">Pricing</h4>
                            </a>
                        </div>
                        <div id="clOne" class="panel-collapse collapse show" aria-labelledby="clOne">
                            <div class="block-body text-black">
                                <ul class="deatil_features text-black">
                                    @if($property->property_type == 1)
                                    <li class="text-black"><strong>Price:</strong> ₹{{ $property->property_cost }}</li>
                                    @else
                                    <li class="text-black"><strong>Rent:</strong> ₹{{ $property->rent }}/month</li>
                                    <li class="text-black"><strong>Advance Amount:</strong> ₹{{ $property->advance_amount }}</li>
                                    @endif

                                </ul>
                            </div>
                        </div>

                    </div>




                    <!-- Single Block Wrap -->
                    {{-- <div class="property_block_wrap style-2">

                        <div class="property_block_wrap_header">
                            <a data-bs-toggle="collapse" data-parent="#loca" data-bs-target="#clSix"
                                aria-controls="clSix" href="javascript:void(0);" aria-expanded="true" class="collapsed">
                                <h4 class="property_block_title">Location</h4>
                            </a>
                        </div>

                        <div id="clSix" class="panel-collapse collapse">
                            <div class="block-body">
                                <div class="map-container">
                                    <iframe
                                        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3560.3838103135677!2d80.87929001488125!3d26.827742183164247!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x399bfe8bc34b51bb%3A0xa3ca86eec63f6f8!2sINFOSYS%20DIGITAL%20COMPUTER%20(Prabhat%20Computer%20Classes)!5e0!3m2!1sen!2sin!4v1680238790732!5m2!1sen!2sin"
                                        width="100%" height="450" style="border:0;" allowfullscreen=""
                                        loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                                </div>

                            </div>
                        </div>

                    </div> --}}

                    <!-- Single Block Wrap -->
                    <div class="property_block_wrap style-2">

                        <div class="property_block_wrap_header">
                            <a data-bs-toggle="collapse" data-parent="#clSev" data-bs-target="#clSev"
                                aria-controls="clOne" href="javascript:void(0);" aria-expanded="true" class="collapsed">
                                <h4 class="property_block_title">Gallery</h4>
                            </a>
                        </div>

                        <div id="clSev" class="panel-collapse collapse">
                            <div class="block-body">
                                <ul class="list-gallery-inline">
                                    @if ($property && $property->images && count($property->images))
                                        @foreach ($property->images as $image)
                                            <li>
                                                <a href="{{ url('storage/' . $image->image_path) }}" class="mfp-gallery"><img
                                                        src="{{ url('storage/' . $image->image_path) }}" class="img-fluid mx-auto"
                                                        alt="" /></a>
                                            </li>
                                        @endforeach
                                    @endif

                                </ul>
                            </div>
                        </div>

                    </div>

                </div>

                <!-- property Sidebar -->
                <div class="col-lg-4 col-md-12 col-sm-12">

                    <div class="details-sidebar">

                    <!-- Agent Detail -->
                    <div class="sides-widget">
                        <div class="sides-widget-header bg-primary">
                            {{-- <div class="agent-photo"><img src="/frontend/assets/img/user-6.jpg" alt=""></div> --}}
                            <div class="sides-widget-details d-flex align-items-center">
                                <i class="fa-solid fa-phone-volume me-3 fs-4"></i>
                                <div>
                                    <h4 class="mb-0">
                                        <a href="tel:{{$property->contact_mobile}}"> {{$property->contact_mobile}} </a>
                                    </h4>
                                    <span class=" small">Phone Number</span>
                                </div>
                            </div>

                            <div class="clearfix"></div>
                        </div>
                    </div>
                    <div class="sides-widget">
                        <div class="sides-widget-header bg-primary">
                            {{-- <div class="agent-photo"><img src="/frontend/assets/img/user-6.jpg" alt=""></div> --}}
                            <div class="sides-widget-details d-flex align-items-center">
                                <i class="fa-solid fa-envelope-circle-check me-3 fs-4"></i>
                                <div>
                                    <h4 class="mb-0">
                                        <a href="mailto:{{$property->contact_email}}" class="text-lowercase"> {{$property->contact_email}} </a>
                                    </h4>
                                    <span class=" small">Email</span>
                                </div>
                            </div>

                            <div class="clearfix"></div>
                        </div>
                    </div>

                    <!-- Mortgage Calculator -->
                    <div class="sides-widget">

                        <!--ad section-->
                        <a href="{{ $ads[11]->link }}" target="_blank">
                            <img src="{{ url('storage/' . $ads[11]->image) }}" class="img-fluid" alt="">
                        </a>


                    </div>


                </div>

                </div>

            </div>
        </div>
    </section>
    <!-- ============================ Property Detail End ================================== -->

    <!-- ============================ Call To Action ================================== -->

    <!-- ============================ Call To Action End ================================== -->

    <!-- Video Modal -->
    <div class="modal fade" id="popup-video" tabindex="-1" role="dialog" aria-labelledby="popupvideo"
        aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content" id="popupvideo">
                <iframe class="embed-responsive-item" class="full-width" height="480"
                    src="https://www.youtube.com/embed/qN3OueBm9F4?rel=0" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
    <!-- End Video Modal -->
@stop
