@extends('frontend.layouts.master')

@section('css')
<style>
    #sell_section, #rent_section {
        transition: all 0.3s ease;
    }

    .property-cost-section {
        border: 1px solid #e0e0e0;
        border-radius: 8px;
        padding: 15px;
        margin: 10px 0;
        background-color: #f9f9f9;
    }
</style>
@stop

@section('content')

<!-- ============================ Page Title Start================================== -->
<div class="page-title">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12">

				<h2 class="ipt-title">Post Your Property for Sale or Rent</h2>
				<span class="ipn-subtitle">Post your Property for Sale or for Rent.  Register now for reaching out to the buyers or tenents</span>

			</div>
		</div>
	</div>
</div>
<!-- ============================ Page Title End ================================== -->

<!-- ============================ All Property ================================== -->
<section class="gray-simple">
	<div class="container">
    @if(session()->has('errors'))
    <div class="alert alert-danger">
        <ul>
            @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
            @endforeach
        </ul>
    </div>
    @endif

		<div class="row">

			<!-- Success/Error Messages -->
			@if(session('success'))
				<div class="col-lg-12 col-md-12 mb-4">
					<div class="alert alert-success alert-dismissible fade show" role="alert">
						<i class="fa-solid fa-circle-check me-2"></i>
						<strong>Success!</strong> {{ session('success') }}
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>
				</div>
			@endif

			@if(session('error'))
				<div class="col-lg-12 col-md-12 mb-4">
					<div class="alert alert-danger alert-dismissible fade show" role="alert">
						<i class="fa-solid fa-circle-exclamation me-2"></i>
						<strong>Error!</strong> {{ session('error') }}
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>
				</div>
			@endif

			@if($errors->any())
				<div class="col-lg-12 col-md-12 mb-4">
					<div class="alert alert-danger alert-dismissible fade show" role="alert">
						<i class="fa-solid fa-circle-exclamation me-2"></i>
						<strong>Please fix the following errors:</strong>
						<ul class="mb-0 mt-2">
							@foreach($errors->all() as $error)
								<li>{{ $error }}</li>
							@endforeach
						</ul>
						<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
					</div>
				</div>
			@endif

			<!-- Submit Form -->
			<div class="col-lg-12 col-md-12">
<form action="{{ route('post_property.store') }}" method="POST" enctype="multipart/form-data">
    @csrf
				<div class="submit-page">

					<!-- Basic Information -->
					<div class="form-submit">
						<h3>Basic Information</h3>
						<div class="submit-section">
							<div class="row">

								<!-- Property Type Radio Buttons - Moved before title -->
								<div class="form-group col-md-12 mb-4">
									<label class="mb-3">Property Type <span class="error"> *</span></label>
									<div class="d-flex gap-4">
										<div class="form-check">
											<input class="form-check-input" type="radio" name="property_type" id="property_sell" value="1" checked>
											<label class="form-check-label fw-semibold" for="property_sell">
												For Sale
											</label>
										</div>
										<div class="form-check">
											<input class="form-check-input" type="radio" name="property_type" id="property_rent" value="2">
											<label class="form-check-label fw-semibold" for="property_rent">
												For Rent
											</label>
										</div>
									</div>
									<small class="text-muted">Select property type to see relevant cost fields</small>
								</div>

								<div class="form-group col-md-12">
									<label class="mb-2">Title<span class="error"> *</span></label>
									<input type="text" class="form-control" name="title" id="title" placeholder="Enter Title ">
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">Builder Type<span class="error"> *</span></label>
									<div class="d-flex gap-3 mb-2">
										<div class="form-check">
											<input class="form-check-input" type="radio" name="builder_type" id="individual" value="individual" checked>
											<label class="form-check-label" for="individual">
												Individual
											</label>
										</div>
										<div class="form-check">
											<input class="form-check-input" type="radio" name="builder_type" id="builder" value="builder">
											<label class="form-check-label" for="builder">
												Builder
											</label>
										</div>
									</div>
									<div id="builder-select-container" style="display: none;">
										<div class="input-group">
											<input type="text" class="form-control open-builder-modal" id="selected-builder-display" placeholder="Select Builder" readonly style="cursor: pointer;">
											<button type="button" class="btn btn-outline-secondary open-builder-modal">
												<i class="fa fa-search"></i>
											</button>
										</div>
										<input type="hidden" id="builder_id" name="builder_id" value="">
									</div>
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">Contact Person <span class="error"> *</span></label>
									<input type="text" class="form-control" name="contact_person" id="contact_person" placeholder="Enter name ">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Mobile<span class="error"> *</span></label>
									<input type="text" class="form-control" name="contact_mobile" id="contact_mobile" placeholder="Enter Mobile ">
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">Email</label>
									<input type="text" class="form-control"name="contact_email" id="contact_email"  placeholder="Email">
								</div>

							</div>
						</div>
					</div>
                    <!-- Basic Information -->
					<div class="form-submit">
						<h3>Property Information</h3>
						<div class="submit-section">
							<div class="row">

								<div class="form-group col-md-6">
									<label class="mb-2">Property Type</label>
									<select id="building_type_id" name="building_type_id" class="form-control">
										<option value="">Select Property Type</option>
									</select>
								</div>

								<div class="form-group col-md-3">
									<label class="mb-2">Area Size<span class="error"> *</span></label>
									<input type="text" class="form-control" name="area_size" id="area_size" placeholder="Enter area">
								</div>
								<div class="form-group col-md-3">
									<label class="mb-2">Area Type<span class="error"> *</span></label>
									<select class="form-control" name="area_type_id" id="area_type_id" required>
										<option value="">Select Area Type</option>
										@foreach($areaTypes as $areaType)
											<option value="{{ $areaType->id }}">{{ $areaType->name }} ({{ $areaType->abbreviation }})</option>
										@endforeach
									</select>
								</div>
								<div class="form-group col-md-6" id="build_year_section">
									<label class="mb-2">Build Year<span class="error"> *</span></label>
									<input type="text" class="form-control" name="build_year" id="build_year" placeholder="Enter Build Year">
								</div>

								<div class="form-group col-md-6" id="facing_section">
									<label class="mb-2">Facing</label>
									<select name="facing" id="facing" class="form-control">
                                        <option value="1">East</option>
                                        <option value="2">North</option>
                                        <option value="3">Northeast</option>
                                        <option value="4">West</option>
                                        <option value="5">South</option>
                                        <option value="6">Northwest</option>
                                        <option value="7">Southeast</option>
                                        <option value="8">Southwest</option>
                                        </select>
								</div>

							</div>
						</div>
					</div>
<!---Aminities--->
                                        <div class="form-group col-md-12">
												<h3>Aminities Features (optional)</h3>
												<div class="o-features">
													<ul class="no-ul-list third-row">
                                                        <li>
                                                            <input type="hidden" name="amenities_parking" value="1">
                                                            <input id="a-1" class="form-check-input" name="amenities_parking" type="checkbox" value="1">
                                                            <label for="a-1" class="form-check-label">Carparking</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_swimming_pool" value="1">
                                                            <input id="a-2" class="form-check-input" name="amenities_swimming_pool" type="checkbox" value="1">
                                                            <label for="a-2" class="form-check-label">Swimming Pool</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_lift" value="1">
                                                            <input id="a-3" class="form-check-input" name="amenities_lift" type="checkbox" value="1">
                                                            <label for="a-3" class="form-check-label">Lift</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_gym" value="1">
                                                            <input id="a-4" class="form-check-input" name="amenities_gym" type="checkbox" value="1">
                                                            <label for="a-4" class="form-check-label">Gym</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_spa" value="1">
                                                            <input id="a-5" class="form-check-input" name="amenities_spa" type="checkbox" value="1">
                                                            <label for="a-5" class="form-check-label">Spa</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_power_backup" value="1">
                                                            <input id="a-6" class="form-check-input" name="amenities_power_backup" type="checkbox" value="1">
                                                            <label for="a-6" class="form-check-label">Power Backup</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_water_supply" value="1">
                                                            <input id="a-7" class="form-check-input" name="amenities_water_supply" type="checkbox" value="1">
                                                            <label for="a-7" class="form-check-label">24x7 Water Supply</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_security" value="1">
                                                            <input id="a-8" class="form-check-input" name="amenities_security" type="checkbox" value="1">
                                                            <label for="a-8" class="form-check-label">Security/Watchman</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_cctv" value="1">
                                                            <input id="a-9" class="form-check-input" name="amenities_cctv" type="checkbox" value="1">
                                                            <label for="a-9" class="form-check-label">CCTV Surveillance</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_clubhouse" value="1">
                                                            <input id="a-10" class="form-check-input" name="amenities_clubhouse" type="checkbox" value="1">
                                                            <label for="a-10" class="form-check-label">Clubhouse</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_indoor_games" value="1">
                                                            <input id="a-11" class="form-check-input" name="amenities_indoor_games" type="checkbox" value="1">
                                                            <label for="a-11" class="form-check-label">Indoor Games Room</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_outdoor_games" value="1">
                                                            <input id="a-12" class="form-check-input" name="amenities_outdoor_games" type="checkbox" value="1">
                                                            <label for="a-12" class="form-check-label">Outdoor Sports</label>
                                                        </li>
                                                        <li>
                                                            <input type="hidden" name="amenities_kids_play" value="1">
                                                            <input id="a-13" class="form-check-input" name="amenities_kids_play" type="checkbox" value="1">
                                                            <label for="a-13" class="form-check-label">Kids Play Area</label>
                                                        </li>
                                                    </ul>

												</div>
											</div>

											<!-- Other Amenities Text Field -->
											<div class="form-group col-md-12">
												<label class="mb-2">Other Amenities</label>
												<textarea class="form-control" name="other_amenities" id="other_amenities" rows="3" placeholder="Enter any other amenities not listed above (e.g., Garden, Terrace, Balcony, etc.)"></textarea>
												<small class="text-muted">Optional: Mention any additional amenities or features</small>
											</div>
                                            <!--Features -->
                    <div class="form-submit" id="features_section">
						<h3> Features</h3>
						<div class="submit-section">
							<div class="row">

								<div class="form-group col-md-6">
									<label class="mb-2">No.of Bedrooms<span class="error"> *</span> </label>
									<input type="text" class="form-control" name="no_of_bed_rooms" id="no_of_bed_rooms" placeholder="Enter No.of Bedrooms">
								</div>


								<div class="form-group col-md-6">
									<label class="mb-2">No. of Bathrooms <span class="error"> *</span></label>
									<input type="text" class="form-control" name="no_of_bath_rooms" id="no_of_bath_rooms" placeholder="Enter No.of Bathrooms">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">No. of Balconies<span class="error"> *</span> </label>
									<input type="text" class="form-control" name="no_of_balconies" id="no_of_balconies" placeholder="Enter No.of Balconies">
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">No. of Car Parking <span class="error"> *</span></label>
									<input type="text" class="form-control"name="no_of_car_parkings" id="no_of_car_parkings" placeholder="Enter No.of Car Parking">
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">No. of Floors<span class="error"> *</span></label>
									<input type="text" class="form-control"name="no_of_floors" id="no_of_floors" placeholder="Enter No.of Floors">
								</div>

							</div>
						</div>
					</div>
                    <div class="form-submit">
						<h3> Costing</h3>
						<div class="submit-section">
							<div class="row">

								<!-- Cost Fields Section -->
								<div class="col-md-12">
									<div class="property-cost-section">
										<!-- Sell Section - Show by default for Sell property type -->
										<div id="sell_section" class="row">
											<div class="form-group col-md-6">
												<label class="mb-2">Selling Price <span class="error"> *</span></label>
												<input type="text" class="form-control" name="property_cost" id="property_cost" placeholder="Enter Selling Price">
											</div>
										</div>

										<!-- Rent Section - Hide by default -->
										<div id="rent_section" class="row" style="display: none;">
											<div class="form-group col-md-6">
												<label class="mb-2">Advance Amount <span class="error"> *</span></label>
												<input type="text" class="form-control" name="advance_amount" id="advance_amount" placeholder="Enter Advance Amount">
											</div>
											<div class="form-group col-md-6">
												<label class="mb-2">Monthly Rent <span class="error"> *</span></label>
												<input type="text" class="form-control" name="rent" id="rent" placeholder="Enter Monthly Rent">
											</div>
										</div>
									</div>
								</div>

							</div>
						</div>
					</div>

					<!-- Location -->
					<div class="form-submit">
						<h3>Location</h3>
						<div class="submit-section">
							<div class="row">

								<div class="form-group col-md-6">
									<label class="mb-2">State<span class="error"> *</span></label>
									<select class="form-control" id="state_id" name="state_id">
                                        <option value="">Select State</option>
                                    </select>
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">District<span class="error"> *</span></label>
									<select class="form-control" id="district_id" name="district_id">
                                        <option value="">Select District</option>
                                    </select>
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">Area<span class="error"> *</span></label>
									<select class="form-control" id="area_id" name="area_id">
                                        <option value="">Select Area</option>
                                    </select>
								</div>

								<div class="form-group col-md-6">
									<label class="mb-2">Address<span class="error"> *</span></label>
									<input type="text" class="form-control"name="address" id="address" placeholder="Enter Address">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Nearest Landmark</label>
									<input type="text" class="form-control" name="nearest_landmark" id="nearest_landmark" placeholder="Enter Nearest Landmark">
								</div>
								<div class="form-group col-md-6">
									<label class="mb-2">Distance</label>
									<input type="text" class="form-control" name="distance" id="distance" placeholder="Enter Distance (e.g., 2 km from Metro Station)">
								</div>

							</div>
						</div>
					</div>

					<!-- Verified Listing Upgrade -->
					<div class="form-submit">
						<div class="verified-listing-upgrade">
							<div class="row">
								<div class="col-lg-12">
									<div class="upgrade-card border rounded-3 p-4 mb-4" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; position: relative; overflow: hidden;">
										<!-- Background Pattern -->
										<div style="position: absolute; top: -50px; right: -50px; width: 150px; height: 150px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.3;"></div>
										<div style="position: absolute; bottom: -30px; left: -30px; width: 100px; height: 100px; background: rgba(255,255,255,0.1); border-radius: 50%; opacity: 0.2;"></div>

										<div class="row align-items-center">
											<div class="col-lg-8 col-md-7">
												<div class="upgrade-content position-relative">
													<div class="d-flex align-items-center mb-3">
														<div class="verified-badge me-3" style="background: rgba(255,255,255,0.2); padding: 8px 16px; border-radius: 25px; font-size: 14px; font-weight: 600;">
															<i class="fa-solid fa-shield-check me-2" style="color: #4CAF50;"></i>
															VERIFIED LISTING
														</div>
														<div class="price-tag" style="background: #FF6B35; padding: 6px 12px; border-radius: 15px; font-size: 16px; font-weight: bold;">
															₹500 Only
														</div>
													</div>

													<h4 class="mb-3" style="font-weight: 700; font-size: 24px;">Boost Your Property Visibility!</h4>
													<p class="mb-3" style="font-size: 16px; line-height: 1.6; opacity: 0.95;">
														Get premium exposure and attract more potential buyers/tenants with our verified listing package.
													</p>

													<div class="benefits-list">
														<div class="row">
															<div class="col-md-6">
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">Verified badge & priority listing</span>
																</div>
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">Featured in "Show Verified Only"</span>
																</div>
															</div>
															<div class="col-md-6">
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">NRI Real Estate Club posting</span>
																</div>
																<div class="benefit-item d-flex align-items-center mb-2">
																	<i class="fa-solid fa-check-circle me-2" style="color: #4CAF50; font-size: 16px;"></i>
																	<span style="font-size: 14px;">Social media promotion</span>
																</div>
															</div>
														</div>
													</div>
												</div>
											</div>

											<div class="col-lg-4 col-md-5 text-center">
												<div class="upgrade-action">
													<div class="contact-info mb-3">
														<div class="phone-number" style="background: rgba(255,255,255,0.2); padding: 12px 20px; border-radius: 25px; margin-bottom: 15px;">
															<i class="fa-solid fa-phone me-2" style="color: #4CAF50;"></i>
															<strong style="font-size: 18px;">9121537711</strong>
														</div>
														<p style="font-size: 14px; opacity: 0.9; margin-bottom: 15px;">Call now to upgrade your listing</p>
													</div>

													<button type="button" class="btn btn-light btn-lg px-4 py-2" style="font-weight: 600; border-radius: 25px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);" onclick="window.open('tel:9121537711')">
														<i class="fa-solid fa-phone me-2"></i>
														Call Now
													</button>
												</div>
											</div>
										</div>

										<!-- Detailed Benefits Expandable Section -->
										<div class="mt-4">

											<div  id="detailedBenefitsProperty">
												<div class="detailed-benefits" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 10px; backdrop-filter: blur(10px);">
													<div class="row">
														<div class="col-md-6">
															<h6 style="color: #FFD700; margin-bottom: 15px;">
																<i class="fa-solid fa-star me-2"></i>Premium Visibility
															</h6>
															<ul style="list-style: none; padding: 0;">
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Appears under "Show Verified Only" filter
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Verified badge increases buyer trust
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Higher ranking in search results
																</li>
															</ul>
														</div>
														<div class="col-md-6">
															<h6 style="color: #FFD700; margin-bottom: 15px;">
																<i class="fa-solid fa-share-nodes me-2"></i>Multi-Platform Promotion
															</h6>
															<ul style="list-style: none; padding: 0;">
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Posted on nrireic.com (NRI Real Estate Club)
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Shared on Instagram, Facebook, LinkedIn
																</li>
																<li style="margin-bottom: 8px; font-size: 14px;">
																	<i class="fa-solid fa-arrow-right me-2" style="color: #4CAF50;"></i>
																	Featured on Twitter and YouTube channels
																</li>
															</ul>
														</div>
													</div>

													<div class="mt-3 text-center">
														<small style="opacity: 0.8; font-style: italic;">
															* Social media posting subject to content approval and platform availability
														</small>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>

					<!-- Gallery -->
					<div class="form-submit">
						<h3>Gallery</h3>
						<div class="submit-section">
							<div class="row">
								<div class="form-group col-md-12">
									<label>Upload Gallery Images</label>
									<input type="file" class="form-control" name="images[]" id="images" multiple accept="image/*">
									<small class="text-muted">You can select multiple images (JPG, JPEG, PNG up to 2MB each)</small>
									<div id="preview-images" class="mt-3 d-flex flex-wrap gap-2"></div>
								</div>
							</div>
						</div>
					</div>

					<div class="form-group col-lg-12 col-md-12">
						{{-- <label>GDPR Agreement *</label> --}}
						<ul class="no-ul-list">
							<li>
								<input id="aj_1" class="form-check-input" name="aj_1" type="checkbox">
								<label for="aj_1" class="form-check-label">I accept <a href="/tcpostprop" target="_blank">Tems & Conditions</a>  and  <a href="/privacypolicy" target="_blank">Privacy Policies</a>.</label>
							</li>
						</ul>
					</div>
					<!-- Services -->




					<!-- Contact Information -->
					{{-- <div class="form-submit">
						<h3>Contact Information</h3>
						<div class="submit-section">
							<div class="row">

								<div class="form-group col-md-4">
									<label class="mb-2">Name</label>
									<input type="text" class="form-control">
								</div>

								<div class="form-group col-md-4">
									<label class="mb-2">Email</label>
									<input type="text" class="form-control">
								</div>

								<div class="form-group col-md-4">
									<label class="mb-2">Phone (optional)</label>
									<input type="text" class="form-control">
								</div>

							</div>
						</div>
					</div> --}}

					{{-- <div class="form-group col-lg-12 col-md-12">
						<ul class="no-ul-list">
							<li>
								<input id="aj-1" class="form-check-input" name="aj-1" type="checkbox">
								<label for="aj-1" class="form-check-label">I accept <a href="#">Tems & Conditions</a>  and  <a href="#">Privacy Policies</a>.</label>
							</li>
						</ul>
					</div> --}}

					<div class="form-group col-lg-12 col-md-12">
						<button class="btn btn-primary fw-medium px-5" type="submit" >Submit</button>
					</div>

				</div>
            </form>

			<!-- Builder Selection Modal -->
			<div class="modal fade" id="builderModal" tabindex="-1" aria-labelledby="builderModalLabel" aria-hidden="true">
				<div class="modal-dialog modal-lg">
					<div class="modal-content">
						<div class="modal-header">
							<h5 class="modal-title" id="builderModalLabel">Select or Add Builder</h5>
							<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
						</div>
						<div class="modal-body">
							<!-- Builder Selection Options -->
							<div class="row mb-3">
								<div class="col-md-6">
									<button type="button" class="btn btn-outline-primary w-100" id="select-existing-builder">
										<i class="fa fa-list me-2"></i>Select Existing Builder
									</button>
								</div>
								<div class="col-md-6">
									<button type="button" class="btn btn-outline-success w-100" id="add-new-builder">
										<i class="fa fa-plus me-2"></i>Add New Builder
									</button>
								</div>
							</div>

							<!-- Existing Builder Selection -->
							<div id="existing-builder-section" style="display: none;">
								<h6>Select Builder</h6>
								<select id="modal-builder-select" class="form-control">
									<option value="">Select Builder</option>
									@foreach ($builders as $builder)
										<option value="{{ $builder->id }}">{{ $builder->name }}</option>
									@endforeach
								</select>
								<div class="mt-3">
									<button type="button" class="btn btn-primary" id="confirm-builder-selection">Confirm Selection</button>
									<button type="button" class="btn btn-secondary" id="back-to-options">Back</button>
								</div>
							</div>

							<!-- Add New Builder Form -->
							<div id="add-builder-section" style="display: none;">
								<h6>Add New Builder</h6>
								<form id="add-builder-form">
									@csrf
									<div class="row">
										<div class="col-md-6 mb-3">
											<label class="form-label">Builder Name<span class="text-danger">*</span></label>
											<input type="text" class="form-control" name="name" id="modal-builder-name" placeholder="Enter Builder Name" required>
										</div>
										<div class="col-md-6 mb-3">
											<label class="form-label">Email<span class="text-danger">*</span></label>
											<input type="email" class="form-control" name="email" id="modal-builder-email" placeholder="Enter Email" required>
										</div>
										<div class="col-md-6 mb-3">
											<label class="form-label">Phone<span class="text-danger">*</span></label>
											<input type="text" class="form-control" name="phone" id="modal-builder-phone" placeholder="Enter Phone" required>
										</div>
										<div class="col-md-6 mb-3">
											<label class="form-label">State<span class="text-danger">*</span></label>
											<select class="form-control" id="modal-builder-state" name="state_id" required>
												<option value="">Select State</option>
												@foreach ($states as $state)
													<option value="{{ $state->id }}">{{ $state->name }}</option>
												@endforeach
											</select>
										</div>
										<div class="col-md-6 mb-3">
											<label class="form-label">District<span class="text-danger">*</span></label>
											<select class="form-control" id="modal-builder-district" name="district_id" required>
												<option value="">Select District</option>
											</select>
										</div>
										<div class="col-md-6 mb-3">
											<label class="form-label">Area<span class="text-danger">*</span></label>
											<select class="form-control" id="modal-builder-area" name="area_id" required>
												<option value="">Select Area</option>
											</select>
										</div>
									</div>
									<div class="mt-3">
										<button type="submit" class="btn btn-success" id="save-new-builder">
											<i class="fa fa-save me-2"></i>Save Builder
										</button>
										<button type="button" class="btn btn-secondary" id="back-to-options-from-add">Back</button>
									</div>
								</form>
							</div>
						</div>
					</div>
				</div>
			</div>

			</div>

		</div>
	</div>

</section>
<!-- ============================ All Property ================================== -->

<!-- ============================ Call To Action ================================== -->
<section class="bg-primary call-to-act-wrap">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">

                <div class="call-to-act">
                    <div class="call-to-act-head">
                        <h3>Want to Become a Real Estate Agent?</h3>
                        <span>We'll help you to grow your career and growth.</span>
                    </div>
                    <a href="/homeservice_registration" class="btn btn-call-to-act" target="_blank" >SignUp Today</a>
                </div>

            </div>
        </div>

	</div>
</section>
<!-- ============================ Call To Action End ================================== -->
@stop
@section('js')

<script src="https://cdn.jsdelivr.net/npm/jquery-validation@1.19.3/dist/jquery.validate.min.js"></script>

    <script>
         $('#property_type, #building_type_id, #facing, #area_type_id').select2();

        // Property Type Dynamic Fields Function
        function togglePropertySections() {
            const value = $('input[name="property_type"]:checked').val();

            console.log('Property type changed to:', value);

            // Hide all sections first with animation
            $('#sell_section').fadeOut(200);
            $('#rent_section').fadeOut(200);

            // Remove validation classes and error messages
            $('#property_cost, #advance_amount, #rent').removeClass('is-invalid');
            $('.invalid-feedback').remove();

            // Clear values when switching
            $('#property_cost, #advance_amount, #rent').val('');

            // Handle Builder Type visibility based on property type
            const builderTypeContainer = $('.form-group:has(input[name="builder_type"])');

            if (value === '2') { // For Rent - hide builder type
                builderTypeContainer.fadeOut(200);
                // Reset builder type to individual and clear builder selection
                $('input[name="builder_type"][value="individual"]').prop('checked', true);
                $('#builder-select-container').hide();
                $('#builder_id').val('');
                $('#selected-builder-display').val('');
                console.log('Hiding builder type for rent property');
            } else { // For Sale - show builder type
                builderTypeContainer.fadeIn(300);
                console.log('Showing builder type for sale property');
            }

            // Show appropriate section after hiding animation completes
            setTimeout(function() {
                if (value === '1') { // Sell
                    $('#sell_section').fadeIn(300);
                    console.log('Showing sell section');
                } else if (value === '2') { // Rent
                    $('#rent_section').fadeIn(300);
                    console.log('Showing rent section');
                }
            }, 250);
        }

        // Radio button change event
        $('input[name="property_type"]').on('change', function() {
            console.log('Radio button change event triggered');
            togglePropertySections();

            // Clear validation errors from cost fields after a delay
            setTimeout(function() {
                $('#property_cost, #advance_amount, #rent').each(function() {
                    $(this).removeClass('error is-invalid');
                    $(this).next('label.error').remove();
                    $(this).next('.invalid-feedback').remove();
                });
            }, 300);
        });



        // Building Type Dynamic Fields Function
        function toggleBuildingTypeSections() {
            const buildingTypeValue = $('#building_type_id').val();

            console.log('Building type changed to:', buildingTypeValue);

            // Agriculture Land (ID: 1) and Open Land (ID: 7) should hide these sections
            if (buildingTypeValue === '1' || buildingTypeValue === '7') {
                // Hide Build Year, Facing, and Features sections
                $('#build_year_section').fadeOut(300);
                $('#facing_section').fadeOut(300);
                $('#features_section').fadeOut(300);

                // Clear values and remove validation classes
                $('#build_year').val('').removeClass('is-invalid').removeClass('error');
                $('#facing').val('').removeClass('is-invalid').removeClass('error');
                $('#no_of_bed_rooms').val('').removeClass('is-invalid').removeClass('error');
                $('#no_of_bath_rooms').val('').removeClass('is-invalid').removeClass('error');
                $('#no_of_balconies').val('').removeClass('is-invalid').removeClass('error');
                $('#no_of_car_parkings').val('').removeClass('is-invalid').removeClass('error');
                $('#no_of_floors').val('').removeClass('is-invalid').removeClass('error');

                // Remove error messages and labels
                $('#build_year_section .invalid-feedback, #build_year_section .error').remove();
                $('#facing_section .invalid-feedback, #facing_section .error').remove();
                $('#features_section .invalid-feedback, #features_section .error').remove();

                // Remove error labels specifically
                $('label[for="build_year"].error').remove();
                $('label[for="facing"].error').remove();
                $('label[for="no_of_bed_rooms"].error').remove();
                $('label[for="no_of_bath_rooms"].error').remove();
                $('label[for="no_of_balconies"].error').remove();
                $('label[for="no_of_car_parkings"].error').remove();
                $('label[for="no_of_floors"].error').remove();

                console.log('Hiding sections for Agriculture/Open Land');
            } else {
                // Show Build Year, Facing, and Features sections for other building types
                $('#build_year_section').fadeIn(300);
                $('#facing_section').fadeIn(300);
                $('#features_section').fadeIn(300);

                console.log('Showing sections for other building types');
            }
        }

        // Building type change event
        $('#building_type_id').on('change', function() {
            console.log('Property type change event triggered');
            toggleBuildingTypeSections();

            // Clear validation errors from fields that might be hidden
            setTimeout(function() {
                $('#build_year, #facing, #no_of_bed_rooms, #no_of_bath_rooms, #no_of_balconies, #no_of_car_parkings, #no_of_floors').each(function() {
                    $(this).removeClass('error is-invalid');
                    $(this).next('label.error').remove();
                    $(this).next('.invalid-feedback').remove();
                });
            }, 350);
        });

        $(document).ready(function() {
            $('#state_id').select2({
                placeholder: 'Select State',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "states",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });
             // Initialize Select2 for district
             $('select[name="district_id"]').select2({
                placeholder: 'Select District',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let stateId = $('select[name="state_id"]').val();
                        if (!stateId) {
                            // Show error message if state is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a state first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "districts",
                            state_id: stateId, // Send selected state_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Prevent selecting district without state
            $('select[name="district_id"]').on('select2:opening', function(e) {
                let stateId = $('select[name="state_id"]').val();
                if (!stateId) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please select a state first!',
                    });
                    e.preventDefault(); // Prevent opening district dropdown
                }
            });
             // Initialize Select2 for area
             $('select[name="area_id"]').select2({
                placeholder: 'Select Area',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        let districtId = $('select[name="district_id"]').val();
                        if (!districtId) {
                            // Show error message if district is not selected
                            Swal.fire({
                                icon: 'error',
                                title: 'Validation Error',
                                text: 'Please select a district first!',
                            });
                            return false; // Prevent the request from being sent
                        }
                        return {
                            get_type: 4,
                            datafrom: "areas_with_pincode",
                            district_id: districtId, // Send selected district_id
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });

            // Prevent selecting district without state
            $('select[name="district_id"]').on('select2:opening', function(e) {
                let stateId = $('select[name="state_id"]').val();
                if (!stateId) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Validation Error',
                        text: 'Please select a state first!',
                    });
                    e.preventDefault(); // Prevent opening district dropdown
                }
            });
            $('select[name="state_id"]').change(function() {
                biller = $(this).val();
                $("#district_id").empty().trigger('change');
                $("#area_id").empty().trigger('change');
            });
            $('select[name="district_id"]').change(function() {
                $("#area_id").empty().trigger('change');
            });
            // Add custom validation methods
            $.validator.addMethod("lettersonly", function(value, element) {
                return this.optional(element) || /^[a-zA-Z\s]+$/.test(value);
            }, "Please enter only letters and spaces");

            $.validator.addMethod("numbersonly", function(value, element) {
                return this.optional(element) || /^[0-9]+$/.test(value);
            }, "Please enter only numbers");

            $.validator.addMethod("phonenumber", function(value, element) {
                return this.optional(element) || /^[5-9]\d{9}$/.test(value);
            }, "Please enter a valid 10-digit mobile number starting with 5-9");

            $.validator.addMethod("year", function(value, element) {
                return this.optional(element) || (/^\d{4}$/.test(value) && parseInt(value) >= 1900 && parseInt(value) <= new Date().getFullYear());
            }, "Please enter a valid 4-digit year between 1900 and current year");


            $.validator.addMethod("notonlynumbers", function(value, element) {
                return this.optional(element) || !/^\d+$/.test(value);
            }, "Name cannot contain only numbers");

            $.validator.addMethod("currency", function(value, element) {
                return this.optional(element) || /^\d+(\.\d{1,2})?$/.test(value);
            }, "Please enter a valid amount");

            // Initialize jQuery validation
            $("form").validate({
                ignore: function(index, element) {
                    // Ignore hidden elements and their parents
                    return $(element).is(':hidden') || $(element).closest(':hidden').length > 0;
                },
                rules: {
                    title: {
                        required: true,
                        minlength: 5,
                        maxlength: 100,
                        notonlynumbers: true
                    },
                    contact_person: {
                        required: true,
                        minlength: 2,
                        maxlength: 50,
                        lettersonly: true
                    },
                    contact_mobile: {
                        required: true,
                        phonenumber: true,
                        minlength: 10,
                        maxlength: 10
                    },
                    contact_email: {
                        email: true,
                        maxlength: 100
                    },
                    property_type: {
                        required: true
                    },
                    building_type_id: {
                        required: true
                    },
                    builder_type: {
                        required: function(element) {
                            return $('input[name="property_type"]:checked').val() === '1'; // Required only for Sale
                        }
                    },
                    builder_id: {
                        required: function(element) {
                            const propertyType = $('input[name="property_type"]:checked').val();
                            const builderType = $('input[name="builder_type"]:checked').val();
                            return propertyType === '1' && builderType === 'builder'; // Required only for Sale and when Builder is selected
                        }
                    },
                    area_size: {
                        required: true,
                        numbersonly: true,
                        min: 1
                    },
                    area_type_id: {
                        required: true
                    },
                    build_year: {
                        required: function(element) {
                            const buildingType = $('#building_type_id').val();
                            return buildingType !== '1' && buildingType !== '7' && $('#build_year_section').is(':visible');
                        },
                        year: true
                    },
                    facing: {
                        required: function(element) {
                            const buildingType = $('#building_type_id').val();
                            return buildingType !== '1' && buildingType !== '7' && $('#facing_section').is(':visible');
                        }
                    },
                    no_of_bed_rooms: {
                        required: function(element) {
                            const buildingType = $('#building_type_id').val();
                            return buildingType !== '1' && buildingType !== '7' && $('#features_section').is(':visible');
                        },
                        numbersonly: true,
                        min: 1,
                        max: 20
                    },
                    no_of_bath_rooms: {
                        required: function(element) {
                            const buildingType = $('#building_type_id').val();
                            return buildingType !== '1' && buildingType !== '7' && $('#features_section').is(':visible');
                        },
                        numbersonly: true,
                        min: 1,
                        max: 20
                    },
                    no_of_balconies: {
                        required: function(element) {
                            const buildingType = $('#building_type_id').val();
                            return buildingType !== '1' && buildingType !== '7' && $('#features_section').is(':visible');
                        },
                        numbersonly: true,
                        min: 0,
                        max: 20
                    },
                    no_of_car_parkings: {
                        required: function(element) {
                            const buildingType = $('#building_type_id').val();
                            return buildingType !== '1' && buildingType !== '7' && $('#features_section').is(':visible');
                        },
                        numbersonly: true,
                        min: 0,
                        max: 20
                    },
                    no_of_floors: {
                        required: function(element) {
                            const buildingType = $('#building_type_id').val();
                            return buildingType !== '1' && buildingType !== '7' && $('#features_section').is(':visible');
                        },
                        numbersonly: true,
                        min: 1,
                        max: 100
                    },
                    property_cost: {
                        required: function() {
                            return $('input[name="property_type"]:checked').val() === '1'; // Required only for Sell
                        },
                        currency: true,
                        min: 1
                    },
                    advance_amount: {
                        required: function() {
                            return $('input[name="property_type"]:checked').val() === '2'; // Required only for Rent
                        },
                        currency: true,
                        min: 1
                    },
                    rent: {
                        required: function() {
                            return $('input[name="property_type"]:checked').val() === '2'; // Required only for Rent
                        },
                        currency: true,
                        min: 1
                    },
                    address: {
                        required: true,
                        minlength: 10,
                        maxlength: 255
                    },
                    nearest_landmark: {
                        required: false,
                        maxlength: 255
                    },
                    distance: {
                        required: false,
                        maxlength: 100
                    },
                    state_id: {
                        required: true
                    },
                    district_id: {
                        required: true
                    },
                    area_id: {
                        required: true
                    },
                    description: {
                        minlength: 20,
                        maxlength: 1000
                    },
                    other_amenities: {
                        maxlength: 500
                    },
                    aj_1: {
                        required: true
                    }
                },
                messages: {
                    title: {
                        required: "Please enter property title",
                        minlength: "Property title must be at least 5 characters long",
                        maxlength: "Property title cannot exceed 100 characters",
                        notonlynumbers: "Property title cannot contain only numbers"
                    },
                    contact_person: {
                        required: "Please enter contact person name",
                        minlength: "Name must be at least 2 characters long",
                        maxlength: "Name cannot exceed 50 characters",
                        lettersonly: "Name should contain only letters and spaces"
                    },
                    contact_mobile: {
                        required: "Please enter mobile number",
                        phonenumber: "Please enter a valid 10-digit mobile number starting with 5-9",
                        minlength: "Mobile number must be exactly 10 digits",
                        maxlength: "Mobile number must be exactly 10 digits"
                    },
                    contact_email: {
                        email: "Please enter a valid email address",
                        maxlength: "Email cannot exceed 100 characters"
                    },
                    property_type: "Please select property type",
                    building_type_id: "Please select property type",
                    area_size: {
                        required: "Please enter area size",
                        numbersonly: "Area size should contain only numbers",
                        min: "Area size must be at least 1"
                    },
                    area_type_id: "Please select area type",
                    build_year: {
                        required: "Please enter build year",
                        year: "Please enter a valid 4-digit year between 1900 and current year"
                    },
                    facing: "Please select facing direction",
                    no_of_bed_rooms: {
                        required: "Please enter number of bedrooms",
                        numbersonly: "Number of bedrooms should contain only numbers",
                        min: "Number of bedrooms must be at least 1",
                        max: "Number of bedrooms cannot exceed 20"
                    },
                    no_of_bath_rooms: {
                        required: "Please enter number of bathrooms",
                        numbersonly: "Number of bathrooms should contain only numbers",
                        min: "Number of bathrooms must be at least 1",
                        max: "Number of bathrooms cannot exceed 20"
                    },
                    no_of_balconies: {
                        required: "Please enter number of balconies",
                        numbersonly: "Number of balconies should contain only numbers",
                        min: "Number of balconies cannot be negative",
                        max: "Number of balconies cannot exceed 20"
                    },
                    no_of_car_parkings: {
                        required: "Please enter number of car parkings",
                        numbersonly: "Number of car parkings should contain only numbers",
                        min: "Number of car parkings cannot be negative",
                        max: "Number of car parkings cannot exceed 20"
                    },
                    no_of_floors: {
                        required: "Please enter number of floors",
                        numbersonly: "Number of floors should contain only numbers",
                        min: "Number of floors must be at least 1",
                        max: "Number of floors cannot exceed 100"
                    },
                    property_cost: {
                        required: "Please enter selling price (required for Sell property)",
                        currency: "Please enter a valid amount",
                        min: "Selling price must be greater than 0"
                    },
                    advance_amount: {
                        required: "Please enter advance amount (required for Rent property)",
                        currency: "Please enter a valid amount",
                        min: "Advance amount must be greater than 0"
                    },
                    rent: {
                        required: "Please enter monthly rent (required for Rent property)",
                        currency: "Please enter a valid amount",
                        min: "Monthly rent must be greater than 0"
                    },
                    address: {
                        required: "Please enter address",
                        minlength: "Address must be at least 10 characters long",
                        maxlength: "Address cannot exceed 255 characters"
                    },
                    nearest_landmark: {
                        maxlength: "Nearest landmark cannot exceed 255 characters"
                    },
                    distance: {
                        maxlength: "Distance cannot exceed 100 characters"
                    },
                    state_id: "Please select a state",
                    district_id: "Please select a district",
                    area_id: "Please select an area",
                    description: {
                        minlength: "Description must be at least 20 characters long",
                        maxlength: "Description cannot exceed 1000 characters"
                    },
                    other_amenities: {
                        maxlength: "Other amenities cannot exceed 500 characters"
                    },
                    builder_type: "Please select builder type (required for Sale property)",
                    builder_id: "Please select a builder (required when Builder type is selected)",
                    aj_1: "Please accept terms and conditions"
                },
                errorPlacement: function(error, element) {
                    if (element.hasClass("select2-hidden-accessible")) {
                        error.insertAfter(element.next('.select2-container'));
                    } else {
                        error.insertAfter(element);
                    }
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });





            // Add Select2 change handlers to clear validation errors
            $('#building_type_id').on('select2:select', function() {
                $(this).valid();
            });

            $('#area_type_id').on('select2:select', function() {
                $(this).valid();
            });

            $('#facing').on('select2:select', function() {
                $(this).valid();
            });

            $('#state_id').on('select2:select', function() {
                $(this).valid();
            });

            $('#district_id').on('select2:select', function() {
                $(this).valid();
            });

            $('#area_id').on('select2:select', function() {
                $(this).valid();
            });

            // Trigger validation on blur for input and textarea fields only
            $(document).on("focusout", "input, textarea", function() {
                var form = $(this).closest("form");
                if (form.data("validator")) {
                    $(this).valid();
                }
            });



            // Builder type change validation
            $('input[name="builder_type"]').on('change', function () {
                $('#builder_id').valid();
            });

            // Property type change validation for builder fields (controlled)
            $('input[name="property_type"]').on('change', function () {
                // Only validate builder fields after a delay to avoid multiple error messages
                setTimeout(function() {
                    var propertyType = $('input[name="property_type"]:checked').val();
                    if (propertyType === '1') { // Only validate for Sale properties
                        $('input[name="builder_type"]:checked').valid();
                        if ($('input[name="builder_type"]:checked').val() === 'builder') {
                            $('#builder_id').valid();
                        }
                    }
                }, 500);
            });

            // Real-time input restrictions
            // Phone number - only allow digits and limit to 10 characters
            $('#contact_mobile').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '').slice(0, 10);
            });

            // Build year - only allow digits and limit to 4 characters
            $('#build_year').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '').slice(0, 4);
            });



            // Numeric fields - only allow digits
            $('#area_size, #no_of_bed_rooms, #no_of_bath_rooms, #no_of_balconies, #no_of_car_parkings, #no_of_floors').on('input', function() {
                this.value = this.value.replace(/[^0-9]/g, '');
            });

            // Currency fields - allow digits and decimal point
            $('#property_cost, #advance_amount, #rent').on('input', function() {
                this.value = this.value.replace(/[^0-9.]/g, '');
                // Ensure only one decimal point
                var parts = this.value.split('.');
                if (parts.length > 2) {
                    this.value = parts[0] + '.' + parts.slice(1).join('');
                }
            });

            // Contact person - only allow letters and spaces
            $('#contact_person').on('input', function() {
                this.value = this.value.replace(/[^a-zA-Z\s]/g, '');
            });

            // Property title - prevent only numbers
            $('#title').on('input', function() {
                var value = this.value;
                if (/^\d+$/.test(value)) {
                    $(this).addClass('is-invalid');
                    if (!$(this).next('.invalid-feedback').length) {
                        $(this).after('<span class="invalid-feedback">Property title cannot contain only numbers</span>');
                    }
                } else {
                    $(this).removeClass('is-invalid');
                    $(this).next('.invalid-feedback').remove();
                }
            });

        });
  </script>
<script>
     $(document).ready(function() {
            // Initialize Select2 for building type
            $('#building_type_id').select2({
                placeholder: 'Select Property Type',
                width: "100%",
                ajax: {
                    url: '/api/fetch_data_other',
                    dataType: 'json',
                    delay: 250,
                    type: "POST",
                    data: function(params) {
                        return {
                            get_type: 4,
                            datafrom: "building_types",
                            q: params.term // search term
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data
                        };
                    },
                    cache: true
                },
                allowClear: true
            });
        });
        </script>
  <script>
    $(document).ready(function() {
        // Existing Select2 and validation code...


        // Image preview
        $('#images').on('change', function() {
            $('#preview-images').html('');
            if (this.files) {
                let filesAmount = this.files.length;
                for (let i = 0; i < filesAmount; i++) {
                    let reader = new FileReader();
                    reader.onload = function(event) {
                        $('#preview-images').append(`
                            <div class="position-relative" style="width: 100px; height: 100px;">
                                <img src="${event.target.result}" class="img-fluid" style="width: 100px; height: 100px; object-fit: cover;">
                            </div>
                        `);
                    }
                    reader.readAsDataURL(this.files[i]);
                }
            }
        });

        // Show SweetAlert for success message
        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: 'Property Posted Successfully!',
                text: '{{ session('success') }}',
                confirmButtonText: 'OK',
                confirmButtonColor: '#28a745',
                timer: 5000,
                timerProgressBar: true
            });
        @endif

        // Show SweetAlert for error message
        @if(session('error'))
            Swal.fire({
                icon: 'error',
                title: 'Property Posting Failed!',
                text: '{{ session('error') }}',
                confirmButtonText: 'Try Again',
                confirmButtonColor: '#dc3545'
            });
        @endif

        // Initialize building type sections on page load
        toggleBuildingTypeSections();

        // Form submission handler
        $('form').on('submit', function(e) {
            const buildingType = $('#building_type_id').val();

            // If Agriculture Land or Open Land, remove validation requirements from hidden fields
            if (buildingType === '1' || buildingType === '7') {
                // Temporarily disable validation for hidden fields
                $('#build_year, #facing, #no_of_bed_rooms, #no_of_bath_rooms, #no_of_balconies, #no_of_car_parkings, #no_of_floors').each(function() {
                    $(this).rules('remove');
                });
            }
        });

    });
</script>

<script>
$(document).ready(function() {
    // Builder Type Radio Button Functionality
    $('input[name="builder_type"]').change(function() {
        if ($(this).val() === 'builder') {
            $('#builder-select-container').show();
        } else {
            $('#builder-select-container').hide();
            $('#builder_id').val('');
            $('#selected-builder-display').val('');
        }
    });

    // Open Builder Modal - handle both input and button clicks
    $('.open-builder-modal').click(function() {
        $('#builderModal').modal('show');
        showBuilderOptions();
    });

    // Modal event handlers
    $('#builderModal').on('shown.bs.modal', function() {
        // Ensure proper initialization when modal is fully shown
        showBuilderOptions();
    });

    $('#builderModal').on('hidden.bs.modal', function() {
        // Clean up when modal is closed
        showBuilderOptions();
    });

    // Show initial builder options
    function showBuilderOptions() {
        // Hide all sections
        $('#existing-builder-section').hide();
        $('#add-builder-section').hide();

        // Show main option buttons
        $('#select-existing-builder, #add-new-builder').show();

        // Reset any Select2 instances
        if ($('#modal-builder-select').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-select').select2('destroy');
        }
        if ($('#modal-builder-state').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-state').select2('destroy');
        }
        if ($('#modal-builder-district').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-district').select2('destroy');
        }
        if ($('#modal-builder-area').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-area').select2('destroy');
        }

        // Reset form values
        $('#modal-builder-select').val('');
        $('#add-builder-form')[0].reset();
    }

    // Select Existing Builder
    $('#select-existing-builder').click(function() {
        $('#select-existing-builder, #add-new-builder').hide();
        $('#existing-builder-section').show();

        // Destroy existing Select2 if it exists
        if ($('#modal-builder-select').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-select').select2('destroy');
        }

        // Initialize Select2 with proper configuration
        $('#modal-builder-select').select2({
            placeholder: 'Select Builder',
            width: "100%",
            dropdownParent: $('#builderModal')
        });
    });

    // Add New Builder
    $('#add-new-builder').click(function() {
        console.log('Add new builder clicked');
        $('#select-existing-builder, #add-new-builder').hide();
        $('#add-builder-section').show();

        // Ensure the section is visible and DOM is ready before initializing Select2
        setTimeout(function() {
            console.log('Initializing builder form after delay');
            initializeBuilderFormSelects();
        }, 300);
    });

    // Back to options buttons
    $('#back-to-options, #back-to-options-from-add').click(function() {
        showBuilderOptions();
    });

    // Confirm Builder Selection
    $('#confirm-builder-selection').click(function() {
        var selectedBuilderId = $('#modal-builder-select').val();
        var selectedBuilderName = $('#modal-builder-select option:selected').text();

        if (selectedBuilderId) {
            $('#builder_id').val(selectedBuilderId);
            $('#selected-builder-display').val(selectedBuilderName);
            $('#builderModal').modal('hide');
        } else {
            alert('Please select a builder');
        }
    });

    // Initialize builder form selects
    function initializeBuilderFormSelects() {
        console.log('Initializing builder form selects...');

        // Destroy existing Select2 instances if they exist
        if ($('#modal-builder-state').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-state').select2('destroy');
        }
        if ($('#modal-builder-district').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-district').select2('destroy');
        }
        if ($('#modal-builder-area').hasClass('select2-hidden-accessible')) {
            $('#modal-builder-area').select2('destroy');
        }

        // Reset dropdowns to initial state
        $('#modal-builder-district').empty().append('<option value="">Select District</option>');
        $('#modal-builder-area').empty().append('<option value="">Select Area</option>');

        // Initialize Select2 first, then attach change handlers
        try {
            // Initialize Select2 for all dropdowns
            $('#modal-builder-state').select2({
                placeholder: 'Select State',
                width: "100%",
                dropdownParent: $('#builderModal'),
                allowClear: true
            });

            $('#modal-builder-district').select2({
                placeholder: 'Select District',
                width: "100%",
                dropdownParent: $('#builderModal'),
                allowClear: true
            });

            $('#modal-builder-area').select2({
                placeholder: 'Select Area',
                width: "100%",
                dropdownParent: $('#builderModal'),
                allowClear: true
            });

            // Attach change handlers AFTER Select2 initialization
            // Use both Select2 events and regular change events for better compatibility
            $('#modal-builder-state').on('select2:select change', function(e) {
                var stateId = $(this).val();
                console.log('State selected/changed:', stateId);
                if (stateId) {
                    loadDistricts(stateId);
                }
            });

            $('#modal-builder-district').on('select2:select change', function(e) {
                var districtId = $(this).val();
                console.log('District selected/changed:', districtId);
                if (districtId) {
                    loadAreas(districtId);
                }
            });

            console.log('Select2 and event handlers initialized successfully');
        } catch (error) {
            console.error('Error initializing Select2:', error);
        }
    }

    // Separate functions for loading districts and areas
    function loadDistricts(stateId) {
        console.log('Loading districts for state:', stateId);

        // Clear and reset district dropdown
        $('#modal-builder-district').empty().append('<option value="">Select District</option>');
        $('#modal-builder-area').empty().append('<option value="">Select Area</option>');

        // Trigger Select2 change to refresh
        $('#modal-builder-district').trigger('change');
        $('#modal-builder-area').trigger('change');

        if (stateId && stateId !== '') {
            $.ajax({
                url: '/api/get-districts/' + stateId,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log('Districts loaded:', data);
                    $('#modal-builder-district').empty().append('<option value="">Select District</option>');
                    if (data && data.length > 0) {
                        $.each(data, function(key, value) {
                            $('#modal-builder-district').append('<option value="'+ value.id +'">'+ value.name +'</option>');
                        });
                    } else {
                        $('#modal-builder-district').append('<option value="">No districts found</option>');
                    }
                    // Refresh Select2 after adding options
                    $('#modal-builder-district').trigger('change');
                },
                error: function(xhr, status, error) {
                    console.error('Error loading districts:', error, xhr.responseText);
                    $('#modal-builder-district').empty().append('<option value="">Error loading districts</option>');
                    $('#modal-builder-district').trigger('change');
                }
            });
        }
    }

    function loadAreas(districtId) {
        console.log('Loading areas for district:', districtId);

        // Clear and reset area dropdown
        $('#modal-builder-area').empty().append('<option value="">Select Area</option>');

        // Trigger Select2 change to refresh
        $('#modal-builder-area').trigger('change');

        if (districtId && districtId !== '') {
            $.ajax({
                url: '/api/get-areas/' + districtId,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log('Areas loaded:', data);
                    $('#modal-builder-area').empty().append('<option value="">Select Area</option>');
                    if (data && data.length > 0) {
                        $.each(data, function(key, value) {
                            var displayText = value.name;
                            if (value.pincode) {
                                displayText += ' - ' + value.pincode;
                            }
                            $('#modal-builder-area').append('<option value="'+ value.id +'">'+ displayText +'</option>');
                        });
                    } else {
                        $('#modal-builder-area').append('<option value="">No areas found</option>');
                    }
                    // Refresh Select2 after adding options
                    $('#modal-builder-area').trigger('change');
                },
                error: function(xhr, status, error) {
                    console.error('Error loading areas:', error, xhr.responseText);
                    $('#modal-builder-area').empty().append('<option value="">Error loading areas</option>');
                    $('#modal-builder-area').trigger('change');
                }
            });
        }
    }

    // Add New Builder Form Submission
    $('#add-builder-form').submit(function(e) {
        e.preventDefault();

        var formData = {
            name: $('#modal-builder-name').val(),
            email: $('#modal-builder-email').val(),
            phone: $('#modal-builder-phone').val(),
            state_id: $('#modal-builder-state').val(),
            district_id: $('#modal-builder-district').val(),
            area_id: $('#modal-builder-area').val(),
            status: 1,
            _token: $('meta[name="csrf-token"]').attr('content')
        };

        $.ajax({
            url: '/api/builders',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Auto-select the new builder
                    $('#builder_id').val(response.builder.id);
                    $('#selected-builder-display').val(response.builder.name);

                    // Add to existing builder select for future use
                    $('#modal-builder-select').append('<option value="'+ response.builder.id +'">'+ response.builder.name +'</option>');

                    // Close modal and show success message
                    $('#builderModal').modal('hide');
                    alert('Builder added successfully and selected!');

                    // Reset form
                    $('#add-builder-form')[0].reset();
                    $('#modal-builder-state, #modal-builder-district, #modal-builder-area').val('').trigger('change');
                } else {
                    alert('Error adding builder: ' + (response.message || 'Unknown error'));
                }
            },
            error: function(xhr, status, error) {
                console.error('Error:', xhr.responseText);
                alert('Error adding builder. Please try again.');
            }
        });
    });


});
</script>

<style>
/* Enhanced Verified Listing Styles */
.verified-listing-upgrade .upgrade-card {
    transition: all 0.3s ease;
    border: 2px solid transparent !important;
}

.verified-listing-upgrade .upgrade-card:hover {
    border-color: rgba(255,255,255,0.3) !important;
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.verified-listing-upgrade .verified-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.verified-listing-upgrade .price-tag {
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

.verified-listing-upgrade .benefit-item {
    transition: all 0.2s ease;
}

.verified-listing-upgrade .benefit-item:hover {
    transform: translateX(5px);
}
</style>

@stop
