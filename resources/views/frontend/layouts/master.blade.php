<!DOCTYPE html>
<html lang="zxx">
	<head>
		<meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
        <meta name="csrf-token" content="{{ csrf_token() }}">

        <title>Real Estate Directory Online</title>
        <link rel="icon" href="/frontend/assets/img/favicon.png" type="image/gif" sizes="18x18">

        <!-- Custom CSS -->
        <link href="/frontend/assets/css/styles.css" rel="stylesheet">

		<!-- Custom Color Option -->
		<link href="/frontend/assets/css/colors.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/@mdi/font/css/materialdesignicons.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://unpkg.com/@icon/themify-icons/themify-icons.css">
        <!-- Google tag (gtag.js) -->
        {{-- <script async src="https://www.googletagmanager.com/gtag/js?id=G-S0Z7ZT026F"></script>
        <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-S0Z7ZT026F');
        </script> --}}

    <!-- Custom Header Styles -->
    <style>
        /* Header Optimization */
        .header {
            padding: 8px 0;
        }

        .nav-brand img {
            max-height: 45px;
            width: auto;
        }

        .nav-menu li a {
            padding: 8px 12px;
            font-size: 14px;
            font-weight: 500;
        }

        .nav-menu-social .dropdown-toggle {
            font-size: 14px;
            padding: 8px 16px !important;
            border-radius: 25px;
        }

        .mobile_nav ul li {
            margin: 0 5px;
        }

        .mobile_nav a {
            padding: 8px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
        }

        /* Responsive adjustments */
        @media (max-width: 1200px) {
            .nav-menu li a {
                padding: 8px 8px;
                font-size: 13px;
            }
        }

        @media (max-width: 992px) {
            .nav-menu li a {
                padding: 8px 6px;
                font-size: 12px;
            }
        }

        /* Dropdown menu styling */
        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 8px 0;
        }

        .dropdown-item {
            padding: 8px 16px;
            font-size: 14px;
        }

        .dropdown-item:hover {
            background-color: #f8f9fa;
        }

        /* Mobile offcanvas styling */
        .offcanvas-body h6 {
            color: #495057;
            font-weight: 600;
            font-size: 16px;
        }

        .offcanvas-body a {
            color: #495057;
            font-size: 15px;
            padding: 8px 0;
            display: block;
        }

        .offcanvas-body a:hover {
            color: #007bff;
        }
    </style>

    </head>

    <body class="blue-skin">

        <!-- Cookie Consent Banner -->
        @include('partials.cookie-consent')

        <div id="preloader"><div class="preloader"><span></span><span></span></div></div>

        <div id="main-wrapper">

            <!-- Start Navigation -->
<div class="header header-light head-shadow">
    <div class="container-fluid px-3">
        <nav id="navigation" class="navigation navigation-landscape">
            <div class="nav-header">
                <a class="nav-brand text-logo" href="/">
                    <img src="/frontend/assets/img/logo.png" alt="" style="max-height: 70px;">
                </a>
                <div class="nav-toggle"></div>
                <div class="mobile_nav d-lg-none">
                    <ul>
                        <li>
                            <a href="/homeservice_registration" class="text-primary" title="Post Home Service">
                                <span class="svg-icon svg-icon-2hx">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M11.2929 2.70711C11.6834 2.31658 12.3166 2.31658 12.7071 2.70711L15.2929 5.29289C15.6834 5.68342 15.6834 6.31658 15.2929 6.70711L12.7071 9.29289C12.3166 9.68342 11.6834 9.68342 11.2929 9.29289L8.70711 6.70711C8.31658 6.31658 8.31658 5.68342 8.70711 5.29289L11.2929 2.70711Z" fill="currentColor"/>
                                        <path d="M11.2929 14.7071C11.6834 14.3166 12.3166 14.3166 12.7071 14.7071L15.2929 17.2929C15.6834 17.6834 15.6834 18.3166 15.2929 18.7071L12.7071 21.2929C12.3166 21.6834 11.6834 21.6834 11.2929 21.2929L8.70711 18.7071C8.31658 18.3166 8.31658 17.6834 8.70711 17.2929L11.2929 14.7071Z" fill="currentColor"/>
                                        <path opacity="0.3" d="M5.29289 8.70711C5.68342 8.31658 6.31658 8.31658 6.70711 8.70711L9.29289 11.2929C9.68342 11.6834 9.68342 12.3166 9.29289 12.7071L6.70711 15.2929C6.31658 15.6834 5.68342 15.6834 5.29289 15.2929L2.70711 12.7071C2.31658 12.3166 2.31658 11.6834 2.70711 11.2929L5.29289 8.70711Z" fill="currentColor"/>
                                        <path opacity="0.3" d="M17.2929 8.70711C17.6834 8.31658 18.3166 8.31658 18.7071 8.70711L21.2929 11.2929C21.6834 11.6834 21.6834 12.3166 21.2929 12.7071L18.7071 15.2929C18.3166 15.6834 17.6834 15.6834 17.2929 15.2929L14.7071 12.7071C14.3166 12.3166 14.3166 11.6834 14.7071 11.2929L17.2929 8.70711Z" fill="currentColor"/>
                                    </svg>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/supplier_registration" class="text-success" title="Post Supplier">
                                <span class="svg-icon svg-icon-2hx">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M6 8.725C6 8.125 6.4 7.725 7 7.725H14L18 11.725V12.925L22 9.725L12.6 2.225C12.2 1.925 11.7 1.925 11.4 2.225L2 9.725L6 12.925V8.725Z" fill="currentColor"/>
                                        <path opacity="0.3" d="M22 9.725C22.4 10.125 22.4 10.725 22 11.125L12.6 18.625C12.2 18.925 11.7 18.925 11.4 18.625L2 11.125C1.6 10.725 1.6 10.125 2 9.725L11.4 2.225C11.7 1.925 12.2 1.925 12.6 2.225L22 9.725ZM7 7.725C6.4 7.725 6 8.125 6 8.725V12.925L2 9.725L11.4 2.225C11.7 1.925 12.2 1.925 12.6 2.225L22 9.725L18 12.925V11.725L14 7.725H7Z" fill="currentColor"/>
                                    </svg>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/post_property" class="text-info" title="Post Property">
                                <span class="svg-icon svg-icon-2hx">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect opacity="0.3" width="12" height="2" rx="1" transform="matrix(-1 0 0 1 15.5 11)" fill="currentColor"></rect>
                                        <path d="M13.6313 11.6927L11.8756 10.2297C11.4054 9.83785 11.3732 9.12683 11.806 8.69401C12.1957 8.3043 12.8216 8.28591 13.2336 8.65206L16.1592 11.2526C16.6067 11.6504 16.6067 12.3496 16.1592 12.7474L13.2336 15.3479C12.8216 15.7141 12.1957 15.6957 11.806 15.306C11.3732 14.8732 11.4054 14.1621 11.8756 13.7703L13.6313 12.3073C13.8232 12.1474 13.8232 11.8526 13.6313 11.6927Z" fill="currentColor"></path>
                                        <path d="M8 5V6C8 6.55228 8.44772 7 9 7C9.55228 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H18C18.5523 5 19 5.44772 19 6V18C19 18.5523 18.5523 19 18 19H11C10.4477 19 10 18.5523 10 18C10 17.4477 9.55228 17 9 17C8.44772 17 8 17.4477 8 18V19C8 20.1046 8.89543 21 10 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H10C8.89543 3 8 3.89543 8 5Z" fill="currentColor"></path>
                                    </svg>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="/post_job" class="text-primary" title="Post Job">
                                <span class="svg-icon svg-icon-2hx">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M20 6H16V4C16 2.89 15.11 2 14 2H10C8.89 2 8 2.89 8 4V6H4C3.45 6 3 6.45 3 7S3.45 8 4 8H5V19C5 20.1 5.9 21 7 21H17C18.1 21 19 20.1 19 19V8H20C20.55 8 21 7.55 21 7S20.55 6 20 6ZM10 4H14V6H10V4Z" fill="currentColor"/>
                                    </svg>
                                </span>
                            </a>
                        </li>
                        <li>
                            <a href="#" class="text-primary" data-bs-toggle="offcanvas" data-bs-target="#offcanvasScrolling" aria-controls="offcanvasScrolling" title="Menu">
                                <span class="svg-icon svg-icon-2hx">
                                    <svg width="22" height="22" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <rect y="6" width="16" height="3" rx="1.5" fill="currentColor"/>
                                        <rect opacity="0.3" y="12" width="8" height="3" rx="1.5" fill="currentColor"/>
                                        <rect opacity="0.3" width="12" height="3" rx="1.5" fill="currentColor"/>
                                    </svg>
                                </span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="nav-menus-wrapper" style="transition-property: none;">
                <ul class="nav-menu">
                    <li><a href="/find_homeservices" target="_blank">Home Services</a></li>
                    <li><a href="/find_suppliers" target="_blank"> Real Estate Suppliers/Vendors</a></li>
                    <li><a href="/properties" target="_blank">Properties</a></li>
                    <li><a href="/jobs" target="_blank">Real Estate Jobs</a></li>
                </ul>
 {{-- <ul class="nav-menu">
                    <li><a href="/">Home Services</a></li>
                    <li><a href="/properties">Properties</a></li>
                    <li><a href="/builders">Builders</a></li>
                    <li><a href="/find_suppliers">Suppliers</a></li>
                    <li><a href="/find_homeservices">Services</a></li>
                    <li><a href="/jobs">Jobs</a></li>
                    <li><a href="/contact">Contact</a></li>
                </ul> --}}
                <ul class="nav-menu nav-menu-social align-to-right d-none d-lg-flex">
                    <li>
                        <a href="/homeservice_registration" target="_blank" class="btn btn-primary me-2 px-3 py-2 d-flex align-items-center text-white">
                            <span class="svg-icon svg-icon-2hx me-2">
                                 <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.3" width="12" height="2" rx="1" transform="matrix(-1 0 0 1 15.5 11)" fill="white"></rect>
                                    <path d="M13.6313 11.6927L11.8756 10.2297C11.4054 9.83785 11.3732 9.12683 11.806 8.69401C12.1957 8.3043 12.8216 8.28591 13.2336 8.65206L16.1592 11.2526C16.6067 11.6504 16.6067 12.3496 16.1592 12.7474L13.2336 15.3479C12.8216 15.7141 12.1957 15.6957 11.806 15.306C11.3732 14.8732 11.4054 14.1621 11.8756 13.7703L13.6313 12.3073C13.8232 12.1474 13.8232 11.8526 13.6313 11.6927Z" fill="white"></path>
                                    <path d="M8 5V6C8 6.55228 8.44772 7 9 7C9.55228 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H18C18.5523 5 19 5.44772 19 6V18C19 18.5523 18.5523 19 18 19H11C10.4477 19 10 18.5523 10 18C10 17.4477 9.55228 17 9 17C8.44772 17 8 17.4477 8 18V19C8 20.1046 8.89543 21 10 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H10C8.89543 3 8 3.89543 8 5Z" fill="white"></path>
                                </svg>
                            </span>
                            <span class="fw-semibold">Post Home Service</span>
                        </a>
                    </li>
                    <li>
                        <a href="/supplier_registration" target="_blank" class="btn btn-primary me-2 px-3 py-2 d-flex align-items-center text-white">
                            <span class="svg-icon svg-icon-2hx me-2">
                                  <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.3" width="12" height="2" rx="1" transform="matrix(-1 0 0 1 15.5 11)" fill="white"></rect>
                                    <path d="M13.6313 11.6927L11.8756 10.2297C11.4054 9.83785 11.3732 9.12683 11.806 8.69401C12.1957 8.3043 12.8216 8.28591 13.2336 8.65206L16.1592 11.2526C16.6067 11.6504 16.6067 12.3496 16.1592 12.7474L13.2336 15.3479C12.8216 15.7141 12.1957 15.6957 11.806 15.306C11.3732 14.8732 11.4054 14.1621 11.8756 13.7703L13.6313 12.3073C13.8232 12.1474 13.8232 11.8526 13.6313 11.6927Z" fill="white"></path>
                                    <path d="M8 5V6C8 6.55228 8.44772 7 9 7C9.55228 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H18C18.5523 5 19 5.44772 19 6V18C19 18.5523 18.5523 19 18 19H11C10.4477 19 10 18.5523 10 18C10 17.4477 9.55228 17 9 17C8.44772 17 8 17.4477 8 18V19C8 20.1046 8.89543 21 10 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H10C8.89543 3 8 3.89543 8 5Z" fill="white"></path>
                                </svg>
                            </span>
                            <span class="fw-semibold">Post Real Estate Supplier/Vendor</span>
                        </a>
                    </li>
                    <li>
                        <a href="/post_property" target="_blank" class="btn btn-primary me-2 px-3 py-2 d-flex align-items-center text-white">
                            <span class="svg-icon svg-icon-2hx me-2">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.3" width="12" height="2" rx="1" transform="matrix(-1 0 0 1 15.5 11)" fill="white"></rect>
                                    <path d="M13.6313 11.6927L11.8756 10.2297C11.4054 9.83785 11.3732 9.12683 11.806 8.69401C12.1957 8.3043 12.8216 8.28591 13.2336 8.65206L16.1592 11.2526C16.6067 11.6504 16.6067 12.3496 16.1592 12.7474L13.2336 15.3479C12.8216 15.7141 12.1957 15.6957 11.806 15.306C11.3732 14.8732 11.4054 14.1621 11.8756 13.7703L13.6313 12.3073C13.8232 12.1474 13.8232 11.8526 13.6313 11.6927Z" fill="white"></path>
                                    <path d="M8 5V6C8 6.55228 8.44772 7 9 7C9.55228 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H18C18.5523 5 19 5.44772 19 6V18C19 18.5523 18.5523 19 18 19H11C10.4477 19 10 18.5523 10 18C10 17.4477 9.55228 17 9 17C8.44772 17 8 17.4477 8 18V19C8 20.1046 8.89543 21 10 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H10C8.89543 3 8 3.89543 8 5Z" fill="white"></path>
                                </svg>
                            </span>
                            <span class="fw-semibold">Post Property</span>
                        </a>
                    </li>
                    <li>
                        <a href="/post_job" target="_blank" class="btn btn-primary me-2 px-3 py-2 d-flex align-items-center text-white">
                            <span class="svg-icon svg-icon-2hx me-2">
                                 <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect opacity="0.3" width="12" height="2" rx="1" transform="matrix(-1 0 0 1 15.5 11)" fill="white"></rect>
                                    <path d="M13.6313 11.6927L11.8756 10.2297C11.4054 9.83785 11.3732 9.12683 11.806 8.69401C12.1957 8.3043 12.8216 8.28591 13.2336 8.65206L16.1592 11.2526C16.6067 11.6504 16.6067 12.3496 16.1592 12.7474L13.2336 15.3479C12.8216 15.7141 12.1957 15.6957 11.806 15.306C11.3732 14.8732 11.4054 14.1621 11.8756 13.7703L13.6313 12.3073C13.8232 12.1474 13.8232 11.8526 13.6313 11.6927Z" fill="white"></path>
                                    <path d="M8 5V6C8 6.55228 8.44772 7 9 7C9.55228 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H18C18.5523 5 19 5.44772 19 6V18C19 18.5523 18.5523 19 18 19H11C10.4477 19 10 18.5523 10 18C10 17.4477 9.55228 17 9 17C8.44772 17 8 17.4477 8 18V19C8 20.1046 8.89543 21 10 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H10C8.89543 3 8 3.89543 8 5Z" fill="white"></path>
                                </svg>
                            </span>
                            <span class="fw-semibold">Post Real Estate Job</span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
    </div>
</div>
<!-- End Navigation -->

<!-- Mobile Post Buttons -->
<div class="container-fluid d-lg-none bg-light border-bottom">
    <div class="container">
        <div class="row py-2">
            <div class="col-12">
                <div class="d-flex flex-wrap gap-2 justify-content-center">
                    <a href="/homeservice_registration" target="_blank" class="btn btn-primary btn-sm d-flex align-items-center text-white">
                        <span class="svg-icon svg-icon-2hx me-1">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M11.2929 2.70711C11.6834 2.31658 12.3166 2.31658 12.7071 2.70711L15.2929 5.29289C15.6834 5.68342 15.6834 6.31658 15.2929 6.70711L12.7071 9.29289C12.3166 9.68342 11.6834 9.68342 11.2929 9.29289L8.70711 6.70711C8.31658 6.31658 8.31658 5.68342 8.70711 5.29289L11.2929 2.70711Z" fill="white"/>
                                <path d="M11.2929 14.7071C11.6834 14.3166 12.3166 14.3166 12.7071 14.7071L15.2929 17.2929C15.6834 17.6834 15.6834 18.3166 15.2929 18.7071L12.7071 21.2929C12.3166 21.6834 11.6834 21.6834 11.2929 21.2929L8.70711 18.7071C8.31658 18.3166 8.31658 17.6834 8.70711 17.2929L11.2929 14.7071Z" fill="white"/>
                                <path opacity="0.3" d="M5.29289 8.70711C5.68342 8.31658 6.31658 8.31658 6.70711 8.70711L9.29289 11.2929C9.68342 11.6834 9.68342 12.3166 9.29289 12.7071L6.70711 15.2929C6.31658 15.6834 5.68342 15.6834 5.29289 15.2929L2.70711 12.7071C2.31658 12.3166 2.31658 11.6834 2.70711 11.2929L5.29289 8.70711Z" fill="white"/>
                                <path opacity="0.3" d="M17.2929 8.70711C17.6834 8.31658 18.3166 8.31658 18.7071 8.70711L21.2929 11.2929C21.6834 11.6834 21.6834 12.3166 21.2929 12.7071L18.7071 15.2929C18.3166 15.6834 17.6834 15.6834 17.2929 15.2929L14.7071 12.7071C14.3166 12.3166 14.3166 11.6834 14.7071 11.2929L17.2929 8.70711Z" fill="white"/>
                            </svg>
                        </span>
                        <span class="fw-semibold small">Service</span>
                    </a>

                    <a href="/supplier_registration" target="_blank" class="btn btn-success btn-sm d-flex align-items-center text-white">
                        <span class="svg-icon svg-icon-2hx me-1">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M6 8.725C6 8.125 6.4 7.725 7 7.725H14L18 11.725V12.925L22 9.725L12.6 2.225C12.2 1.925 11.7 1.925 11.4 2.225L2 9.725L6 12.925V8.725Z" fill="white"/>
                                <path opacity="0.3" d="M22 9.725C22.4 10.125 22.4 10.725 22 11.125L12.6 18.625C12.2 18.925 11.7 18.925 11.4 18.625L2 11.125C1.6 10.725 1.6 10.125 2 9.725L11.4 2.225C11.7 1.925 12.2 1.925 12.6 2.225L22 9.725ZM7 7.725C6.4 7.725 6 8.125 6 8.725V12.925L2 9.725L11.4 2.225C11.7 1.925 12.2 1.925 12.6 2.225L22 9.725L18 12.925V11.725L14 7.725H7Z" fill="white"/>
                            </svg>
                        </span>
                        <span class="fw-semibold small">Supplier</span>
                    </a>

                    <a href="/post_property" target="_blank" class="btn btn-info btn-sm d-flex align-items-center text-white">
                        <span class="svg-icon svg-icon-2hx me-1">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.3" width="12" height="2" rx="1" transform="matrix(-1 0 0 1 15.5 11)" fill="white"></rect>
                                <path d="M13.6313 11.6927L11.8756 10.2297C11.4054 9.83785 11.3732 9.12683 11.806 8.69401C12.1957 8.3043 12.8216 8.28591 13.2336 8.65206L16.1592 11.2526C16.6067 11.6504 16.6067 12.3496 16.1592 12.7474L13.2336 15.3479C12.8216 15.7141 12.1957 15.6957 11.806 15.306C11.3732 14.8732 11.4054 14.1621 11.8756 13.7703L13.6313 12.3073C13.8232 12.1474 13.8232 11.8526 13.6313 11.6927Z" fill="white"></path>
                                <path d="M8 5V6C8 6.55228 8.44772 7 9 7C9.55228 7 10 6.55228 10 6C10 5.44772 10.4477 5 11 5H18C18.5523 5 19 5.44772 19 6V18C19 18.5523 18.5523 19 18 19H11C10.4477 19 10 18.5523 10 18C10 17.4477 9.55228 17 9 17C8.44772 17 8 17.4477 8 18V19C8 20.1046 8.89543 21 10 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H10C8.89543 3 8 3.89543 8 5Z" fill="white"></path>
                            </svg>
                        </span>
                        <span class="fw-semibold small">Property</span>
                    </a>

                    <a href="/post_job" target="_blank" class="btn btn-warning btn-sm d-flex align-items-center text-white">
                        <span class="svg-icon svg-icon-2hx me-1">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M20 6H16V4C16 2.89 15.11 2 14 2H10C8.89 2 8 2.89 8 4V6H4C3.45 6 3 6.45 3 7S3.45 8 4 8H5V19C5 20.1 5.9 21 7 21H17C18.1 21 19 20.1 19 19V8H20C20.55 8 21 7.55 21 7S20.55 6 20 6ZM10 4H14V6H10V4Z" fill="white"/>
                            </svg>
                        </span>
                        <span class="fw-semibold small">Job</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="clearfix"></div>
            <!-- Main Content -->
            <!-- Mobile Offcanvas Menu -->
            <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasScrolling" aria-labelledby="offcanvasScrollingLabel">
                <div class="offcanvas-header">
                    <h5 class="offcanvas-title" id="offcanvasScrollingLabel">Menu</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                </div>
                <div class="offcanvas-body">
                    <div class="mobile-menu">
                        <h6 class="mb-3">Navigation</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2"><a href="/" class="text-decoration-none">🏠 Home</a></li>
                            <li class="mb-2"><a href="/properties" class="text-decoration-none">🏢 Properties</a></li>
                            <li class="mb-2"><a href="/builders" class="text-decoration-none">🏗️ Builders</a></li>
                            <li class="mb-2"><a href="/suppliers" class="text-decoration-none">🚚 Suppliers</a></li>
                            <li class="mb-2"><a href="/homeservices" class="text-decoration-none">🔧 Services</a></li>
                            <li class="mb-2"><a href="/jobs" class="text-decoration-none">💼 Jobs</a></li>
                            <li class="mb-2"><a href="/contact" class="text-decoration-none">📞 Contact</a></li>
                        </ul>

                        <hr class="my-4">

                        <h6 class="mb-3">Post Listings</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <a href="/homeservice_registration" class="text-decoration-none">
                                    <i class="fa-solid fa-tools me-2 text-primary"></i>Post Home Service
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/supplier_registration" class="text-decoration-none">
                                    <i class="fa-solid fa-truck me-2 text-success"></i>Post Supplier
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/post_property" class="text-decoration-none">
                                    <i class="fa-solid fa-home me-2 text-info"></i>Post Property
                                </a>
                            </li>
                            <li class="mb-2">
                                <a href="/post_job" class="text-decoration-none">
                                    <i class="fa-solid fa-briefcase me-2 text-warning"></i>Post Job
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <main>
                @yield('content')
                <?php //echo $hero_content ?? '<!-- Default hero content here -->'; ?>
            </main>

            <footer class="dark-footer skin-dark-footer ">
                <div>
                    <div class="container">
                        <div class="row">

                            <div class="col-lg-3 col-md-4">
                                <div class="footer-widget pt-4 pb-2">
                                    <a class="nav-footer-logo" href="/">
                                        <img src="/frontend/assets/img/logo.png" alt="" width="150">
                                    </a>
                                    <div class="footer-add">
                                        <p>Your trusted platform for all household service needs—find plumbers, electricians, carpenters, and more. Also explore properties for sale and rent with verified listings. Everything you need for your home, in one place.</p>
                                    </div>

                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4">
                                <div class="footer-widget pt-4 pb-2">
                                    <h4 class="widget-title">Navigations</h4>
                                    <ul class="footer-menu">
                                        <li><a href="/aboutus" target="_blank">About Us</a></li>
                                        <li><a href="/contact" target="_blank">Contact</a></li>
                                        <li><a href="/advertise" target="_blank">Advertise Here</a></li>
                                        <li><a href="/maintermsandconditions" target="_blank">Terms and Conditions</a></li>
                                        <li><a href="/privacypolicy" target="_blank">Privacy Policy</a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="col-lg-2 col-md-4">
                                <div class="footer-widget pt-4 pb-2">
                                    <h4 class="widget-title">Quick Links</h4>
                                    <ul class="footer-menu">
                                        <li><a href="/find_homeservices" target="_blank">Home Services</a></li>
                                        <li><a href="/find_suppliers" target="_blank">Real Estate Vendors/Suppliers </a></li>
                                        <li><a href="/properties" target="_blank">Properties </a></li>
                                        <li><a href="/jobs" target="_blank">Jobs </a></li>

                                    </ul>
                                </div>
                            </div>

                            <div class="col-lg-2 col-md-6">
                                <div class="footer-widget pt-4 pb-2">
                                    <h4 class="widget-title">Join Now</h4>
                                    <ul class="footer-menu">
                                        <li><a href="/homeservice_registration" target="_blank"> Post Home Service </a></li>
                                        <li><a href="/supplier_registration" target="_blank"> Post Real Estate Supplier/Vendor </a></li>
                                        <li><a href="/post_property" target="_blank"> Post Property </a></li>
                                        <li><a href="/post_job" target="_blank"> Post Real Estate Job </a></li>
                                    </ul>
                                </div>
                            </div>

                            <div class="col-lg-3 col-md-6">
                                <div class="footer-widget pt-4 pb-2">
                                    <h4 class="widget-title">Contat Information</h4>
                                    <div class="footer-add">
                                        {{-- <p>Hyderabad</p>
                                        <p>+91 9999999999</p> --}}
                                        <p><EMAIL></p>
                                    </div>

                                </div>
                            </div>

                        </div>
                    </div>
                </div>

                <div class="footer-bottom">
                    <div class="container">
                        <div class="row align-items-center">

                            <div class="col-lg-6 col-md-6">
                                <p class="mb-0">© {{ date('Y') }} Real Estate Directory Online. Develop  By <a href="https://vasriyait.com/" target="_blank" class="text-reset">Vasriya IT Solutions</a>.</p>
                            </div>

                            <div class="col-lg-6 col-md-6 text-right">
                                <ul class="footer-bottom-social">
                                    <li><a href="https://x.com/realestatediron" target="_blank"><i class="fa-brands fa-facebook"></i></a></li>
                                    <li><a href="https://x.com/realestatediron" target="_blank"><i class="fa-brands fa-twitter"></i></a></li>
                                    <li><a href="https://www.instagram.com/realestatedirecton/" target="_blank"><i class="fa-brands fa-instagram"></i></a></li>
                                    <li><a href="https://www.linkedin.com/in/real-estate-directory-online-realestate-759308378/" target="_blank"><i class="fa-brands fa-linkedin"></i></a></li>
                                </ul>
                            </div>

                        </div>
                    </div>
                </div>
            </footer>

            <!-- Back to top -->
            <a id="back2Top" class="top-scroll bg-primary" title="Back to top" href="#"><i class="ti ti-arrow-up"></i></a>
            <!-- Back to top -->

        </div>

        <!-- ============================================================== -->
		<!-- All Jquery -->
		<!-- ============================================================== -->
		<script src="/frontend/assets/js/jquery.min.js"></script>
		<script src="/frontend/assets/js/popper.min.js"></script>
		<script src="/frontend/assets/js/bootstrap.min.js"></script>
		<script src="/frontend/assets/js/rangeslider.js"></script>
		<script src="/frontend/assets/js/select2.min.js"></script>
		<script src="/frontend/assets/js/jquery.magnific-popup.min.js"></script>
		<script src="/frontend/assets/js/slick.js"></script>
		<script src="/frontend/assets/js/slider-bg.js"></script>
		<script src="/frontend/assets/js/lightbox.js"></script>
		<script src="/frontend/assets/js/imagesloaded.js"></script>

		<script src="/frontend/assets/js/custom.js"></script>

        <script src="/frontend/assets/js/dropzone.js"></script>
        <script src="/frontend/assets/js/contact.js"></script>
		<!-- New Js -->

        <!-- Date Booking Script -->
		<script src="/frontend/assets/js/moment.min.js"></script>
		<script src="/frontend/assets/js/daterangepicker.js"></script>

        <!-- Map -->
		<script src="https://maps.google.com/maps/api/js?key="></script>
		<script src="/frontend/assets/js/map_infobox.js"></script>
		<script src="/frontend/assets/js/markerclusterer.js"></script>
		<script src="/frontend/assets/js/map.js"></script>

        <script>
			// Check In & Check Out Daterange Script
			$(function() {
			  $('input[name="checkout"]').daterangepicker({
				singleDatePicker: true,
			  });
				$('input[name="checkout"]').val('');
				$('input[name="checkout"]').attr("placeholder","Check Out");
			});
			$(function() {
			  $('input[name="checkin"]').daterangepicker({
				singleDatePicker: true,

			  });
				$('input[name="checkin"]').val('');
				$('input[name="checkin"]').attr("placeholder","Check In");
			});
		</script>

        <script>
            try {
                const counter = document.querySelectorAll('.counter-value');
                const speed = 2500; // The lower the slower

                counter.forEach(counter_value => {
                    const updateCount = () => {
                        const target = +counter_value.getAttribute('data-target');
                        const count = +counter_value.innerText;

                        // Lower inc to slow and higher to slow
                        var inc = target / speed;

                        if (inc < 1) {
                            inc = 1;
                        }

                        // Check if target is reached
                        if (count < target) {
                            // Add inc to count and output in counter_value
                            counter_value.innerText = (count + inc).toFixed(0);
                            // Call function every ms
                            setTimeout(updateCount, 1);
                        } else {
                            counter_value.innerText = target;
                        }
                    };

                    updateCount();
                });
            } catch (err) {

            }
        </script>

        <script>
			function openFilterSearch() {
				document.getElementById("filter_search").style.display = "block";
			}
			function closeFilterSearch() {
				document.getElementById("filter_search").style.display = "none";
			}
		</script>

        <script>
			  $(document).ready(function(){
				$("#showbutton").click(function(){
				$("#showing").slideToggle("slow");
			  });
			  });
		</script>

        <script>
            window.onscroll = function () {
                scrollFunction();
            };

            function scrollFunction() {
                var mybutton = document.getElementById("back-to-top");
                if(mybutton!=null){
                    if (document.body.scrollTop > 500 || document.documentElement.scrollTop > 500) {
                        mybutton.classList.add("block");
                        mybutton.classList.remove("hidden");
                    } else {
                        mybutton.classList.add("hidden");
                        mybutton.classList.remove("block");
                    }
                }
            }

            function topFunction() {
                document.body.scrollTop = 0;
                document.documentElement.scrollTop = 0;
            }
        </script>
<script>
    const currentPath = window.location.pathname;
    console.log(currentPath);

    // Find and highlight the active submenu item
    const subMenuItems = document.querySelectorAll('.sub-menu-item');
    subMenuItems.forEach((item) => {
        if (item.getAttribute('href') === currentPath) {
            item.classList.add('active');

            // Highlight all parent menus recursively
            let parentMenu = item.closest('.parent-menu-item');
            while (parentMenu && !parentMenu.classList.contains('processed')) {
                const parentLink = parentMenu.querySelector('a');
                if (parentLink) {
                    parentLink.classList.add('active');
                }
                parentMenu.classList.add('processed'); // Mark as processed to avoid re-processing
                parentMenu = parentMenu.closest('.parent-parent-menu-item');
            }

            // Highlight the top-level parent menu
            const topLevelMenu = item.closest('.parent-parent-menu-item');
            if (topLevelMenu) {
                const topLevelLink = topLevelMenu.querySelector('.home-link');
                if (topLevelLink) {
                    topLevelLink.classList.add('active');
                }
            }
        }
    });
</script>
@yield('js')
	</body>
</html>
