@extends('frontend.layouts.master')
@section('content')

<style>
.classical-cats-wrap {
    height: 180px !important;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.classical-cats-wrap:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.15) !important;
}

.classical-cats-boxs {
    height: 100%;
    min-height: 180px;
}

.classical-cats-wrap-content h4 {
    font-size: 14px !important;
    font-weight: 600;
    line-height: 1.3;
    height: 40px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    margin-bottom: 8px;
}

.classical-cats-icon {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px auto;
    flex-shrink: 0;
}

@media (max-width: 576px) {
    .classical-cats-wrap {
        height: 160px !important;
    }

    .classical-cats-boxs {
        min-height: 160px;
        padding: 15px !important;
    }

    .classical-cats-icon {
        width: 60px;
        height: 60px;
        margin-bottom: 10px;
    }

    .classical-cats-wrap-content h4 {
        font-size: 13px !important;
        height: 35px;
    }
}
</style>

<!-- ============================ Page Title Start================================== -->
<div class="page-title">
	<div class="container">
		<div class="row">
			<div class="col-lg-12 col-md-12">

				<h2 class="ipt-title">Explore Trusted Home Services Across All Service Categories</h2>
				<span class="ipn-subtitle">Browse a wide range of service categories and find top-rated home services for every need—plumbing, carpentry, electrical, cleaning, and more. View profiles, compare ratings, and choose the right professional for your task.</span>

			</div>
		</div>
	</div>
</div>
<!-- ============================ Page Title End ================================== -->

<!-- ============================ All Property ================================== -->
<section class="gray-simple">

	<div class="container">

		<div class="row">
			<div class="col-lg-12 col-md-12">
				<div class="filter_search_opt">
					<a href="javascript:void(0);" class="btn btn-dark full-width mb-4" onclick="openFilterSearch()">
						<span class="svg-icon text-light svg-icon-2hx me-2">
							<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M19.0759 3H4.72777C3.95892 3 3.47768 3.83148 3.86067 4.49814L8.56967 12.6949C9.17923 13.7559 9.5 14.9582 9.5 16.1819V19.5072C9.5 20.2189 10.2223 20.7028 10.8805 20.432L13.8805 19.1977C14.2553 19.0435 14.5 18.6783 14.5 18.273V13.8372C14.5 12.8089 14.8171 11.8056 15.408 10.964L19.8943 4.57465C20.3596 3.912 19.8856 3 19.0759 3Z" fill="currentColor"/>
							</svg>
						</span>Open Filter Option
					</a>
				</div>
			</div>
		</div>

		<div class="row">

			<!-- property-sidebar code  -->
			<!-- property Sidebar -->


			<div class="col-lg-12 col-md-12 list-layout">



				<div class="row justify-content-center gx-3 gy-3">

                    <!-- property-type code  -->
                        @foreach ($categories as $category)
                            <div class="col-xl-2 col-lg-3 col-md-4 col-sm-6 col-6">
                                <div class="category-item h-100">
                                    <a class="category-link bg-white rounded-3 px-3 py-3 d-flex flex-column h-100 text-decoration-none border" href="/homeservices/{{ $category->slug }}">
                                        {{-- All images and icons removed --}}
                                        {{-- <img src="{{ url('storage/' . $category->image) }}" alt="{{ $category->name }}" width="50" height="50" style="object-fit: contain;"> --}}

                                        <div class="category-content text-center">
                                            <h4 class="mb-2 text-dark fw-semibold" style="font-size: 16px; line-height: 1.4;">{{ $category->name }}</h4>
                                            <p class="mb-0 text-muted small text-black">{{ $category->homeservice_count }} {{ $category->homeservice_count == 1 ? 'Service' : 'Services' }}</p>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        @endforeach

                </div>

				<!-- Pagination -->


			</div>

		</div>
	</div>
</section>
<!-- ============================ All Property ================================== -->

<!-- ============================ Call To Action ================================== -->
<section class="bg-primary call-to-act-wrap">
	<div class="container">

		<!-- estate-agent code  -->
		<div class="row">
            <div class="col-lg-12">

                <div class="call-to-act">
                    <div class="call-to-act-head">
                        <h3>Want to Become a Real Estate Agent?</h3>
                        <span>We'll help you to grow your career and growth.</span>
                    </div>
                    <a href="/homeservice_registration" class="btn btn-call-to-act" target="_blank" >SignUp Today</a>
                </div>

            </div>
        </div>

	</div>
</section>
<!-- ============================ Call To Action End ================================== -->

<style>
/* Simplified Category Cards - Text Only */
.category-item {
    margin-bottom: 16px;
}

.category-link {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef !important;
    min-height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.category-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0,123,255,0.15);
    border-color: #007bff !important;
    background-color: #f8f9ff !important;
}

.category-content {
    width: 100%;
}

.category-content h4 {
    margin-bottom: 8px;
    color: #333;
    transition: color 0.3s ease;
}

.category-link:hover .category-content h4 {
    color: #007bff;
}

.category-content p {
    font-size: 13px;
    color: #6c757d;
    margin: 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .category-link {
        min-height: 70px;
        padding: 15px 20px !important;
    }

    .category-content h4 {
        font-size: 15px !important;
    }

    .category-content p {
        font-size: 12px !important;
    }
}

@media (max-width: 576px) {
    .category-link {
        min-height: 60px;
        padding: 12px 15px !important;
    }

    .category-content h4 {
        font-size: 14px !important;
        line-height: 1.3 !important;
    }

    .category-content p {
        font-size: 11px !important;
    }
}

/* Grid Layout */
.row.justify-content-center.gx-3.gy-3 {
    margin: 0 -8px;
}

.row.justify-content-center.gx-3.gy-3 > [class*="col-"] {
    padding: 0 8px;
}

/* Focus States for Accessibility */
.category-link:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Loading State */
.category-link.loading {
    opacity: 0.6;
    pointer-events: none;
}
</style>

@stop
