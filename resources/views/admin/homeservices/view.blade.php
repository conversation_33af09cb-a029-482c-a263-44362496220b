@extends('admin.layouts.master')
@section('title', 'Clients View- Paidash')
@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .verification-toggle:checked {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
        }
        .verification-toggle:not(:checked) {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
        }
        .verification-label {
            font-weight: 600;
            transition: color 0.3s ease;
        }
        .verification-label.text-success {
            color: #28a745 !important;
        }
        .verification-label.text-danger {
            color: #dc3545 !important;
        }
        .form-switch .form-check-input:disabled {
            opadistrict: 0.6;
            cursor: not-allowed;
        }
    </style>
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Home Service</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/admin/homeservices" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Home Service
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Home Service</li>
            </ul>
        </div>


        <div class="row gy-4">

            <div class="col-lg-12">
                <div class="card h-100">
                    <div class="card-body p-24">
                        <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="pills-tab"
                            role="tablist">

                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24 active" id="homeservice-information"
                                    data-bs-toggle="pill" data-bs-target="#homeservice-information-d" type="button" role="tab"
                                    aria-controls="homeservice-information-d" aria-selected="false" tabindex="-1">
                                    Home Service Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="service"
                                    data-bs-toggle="pill" data-bs-target="#service-d" type="button" role="tab"
                                    aria-controls="service-d" aria-selected="false" tabindex="-1">
                                    Services
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="images"
                                    data-bs-toggle="pill" data-bs-target="#images-d" type="button"
                                    role="tab" aria-controls="images-d" aria-selected="false"
                                    tabindex="-1">
                                    Images
                                </button>
                            </li>

                        </ul>

                        <div class="tab-content" id="pills-tabContent">

                            <div class="tab-pane fade show active" id="homeservice-information-d" role="tabpanel"
                                aria-labelledby="homeservice-information" tabindex="0">
                                <div class="user-grid-card position-relative border radius-16 overflow-hidden bg-base h-100">
                                    <img src="./assets/images/user-grid/user-grid-bg1.png" alt="" class="w-100 object-fit-cover">
                                    <div class="pb-24 ms-16 mb-24 me-16 ">
                                        <div class="d-flex align-items-center justify-content-between px-4 py-3 border-bottom">
                                            <div class="d-flex align-items-center gap-3">
                                                @if ($homeservice->logo && Storage::exists('public/' . $homeservice->logo))
                                                    <img src="{{ asset('storage/' . $homeservice->logo) }}" alt="Profile Image"
                                                        class="border border-white border-width-2-px w-64 h-64 rounded-circle object-fit-cover" width="50px">
                                                @else
                                                    <div class="w-64 h-64 rounded-circle d-flex justify-content-center align-items-center bg-info-100 text-info-600 fw-bold fs-4 px-16 py-4">
                                                        {{ strtoupper(substr($homeservice->business_title, 0, 1)) }}
                                                    </div>
                                                @endif

                                                <div>
                                                    <h5 class="mb-1 d-flex align-items-center flex-wrap">
                                                        <span class="me-2">{{ $homeservice->business_title }}</span>
                                                        @if($homeservice->verified == 1)
                                                            <span class="badge bg-success d-flex align-items-center" title="Verified Business" id="verificationBadge">
                                                                <iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Verified
                                                            </span>
                                                        @else
                                                            <span class="badge bg-warning d-flex align-items-center" title="Not Verified" id="verificationBadge">
                                                                <iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Not Verified
                                                            </span>
                                                        @endif
                                                    </h5>
                                                </div>
                                            </div>

                                        </div>


                                        <div class="row mt-4 gy-4">
                                            <!-- Business Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Business Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Business Name:</strong> <span class="text-muted">{{ $homeservice->business_title }}</span></li>
                                                            <li><strong>Category:</strong> <span class="text-muted">{{ $homeservice->homeservicecategory->name }}</span></li>
                                                            <li><strong>Description:</strong> <span class="text-muted">{{ $homeservice->description }}</span></li>
                                                            <li class="d-flex align-items-center justify-content-between">
                                                                <strong>Verification Status:</strong>
                                                                <div class="form-switch switch-success d-flex align-items-center">
                                                                    <input class="form-check-input verification-toggle" type="checkbox" role="switch"
                                                                           id="verificationSwitch{{ $homeservice->id }}"
                                                                           data-id="{{ $homeservice->id }}"
                                                                           {{ $homeservice->verified == 1 ? 'checked' : '' }}>
                                                                    <label class="form-check-label ms-2 verification-label {{ $homeservice->verified == 1 ? 'text-success' : 'text-danger' }}" for="verificationSwitch{{ $homeservice->id }}">
                                                                        {{ $homeservice->verified == 1 ? 'Verified' : 'Not Verified' }}
                                                                    </label>
                                                                </div>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Contact Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:phone" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Contact Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Mobile:</strong> <span class="text-muted">{{ $homeservice->phone }}</span></li>
                                                            <li><strong>email:</strong> <span class="text-muted">{{ $homeservice->email }}</span></li>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </div> <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:home-map-marker" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Address
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">

                                                            <li><strong>Address:</strong> <span class="text-muted">{{ $homeservice->address }}</span></li>

                                                            <li><strong>Area:</strong> <span class="text-muted">{{ $homeservice->area->name ?? 'NA' }}{{ $homeservice->area->pincode ? ' : ' . $homeservice->area->pincode : '' }}</span></li>
                                                            <li><strong>District:</strong> <span class="text-muted">{{ $homeservice->district->name ?? 'NA' }}</span></li>
                                                            <li><strong>State:</strong> <span class="text-muted">{{ $homeservice->state->name ?? 'NA' }}</span></li>
                                                            @if($homeservice->nearest_landmark)
                                                            <li><strong>Nearest Landmark:</strong> <span class="text-muted">{{ $homeservice->nearest_landmark }}</span></li>
                                                            @endif
                                                            @if($homeservice->distance)
                                                            <li><strong>Distance:</strong> <span class="text-muted">{{ $homeservice->distance }}</span></li>
                                                            @endif
                                                            {{-- <li>
                                                                <strong>Location:</strong>
                                                                @php
                                                                    $mapLink = !empty($homeservice->lang_lat)
                                                                        ? 'https://www.google.com/maps?q=' . urlencode($homeservice->lat_long)
                                                                        : '#';
                                                                @endphp
                                                                <a href="{{ $mapLink }}"
                                                                class="d-flex align-center text-decoration-none {{ empty($homeservice->lat_long) ? 'text-muted' : 'text-success' }}"
                                                                target="_blank" {{ empty($homeservice->lat_long) ? 'aria-disabled=true' : '' }}>
                                                                    <iconify-icon icon="mdi:map-marker" class="me-1" style="font-size: 18px;"></iconify-icon>
                                                                    {{ empty($homeservice->lat_long) ? 'No Location' : 'View on Map' }}
                                                                </a>
                                                            </li> --}}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Additional Details Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:share-variant" class="me-2" style="font-size: 22px;"></iconify-icon>
                                                        Social Media
                                                    </div>

                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:facebook" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $homeservice->facebook }}">{{ $homeservice->facebook }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:twitter" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $homeservice->twitter }}">{{ $homeservice->twitter }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:linkedin" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $homeservice->linked_in }}">{{ $homeservice->linked_in }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:youtube" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $homeservice->youtube }}">{{ $homeservice->youtube }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:instagram" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $homeservice->instagram }}">{{ $homeservice->instagram }}</a>
                                                            </li>
                                                            <li style="display: flex; align-items: center; gap: 8px;">
                                                                <iconify-icon icon="mdi:web" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                                <a href="{{ $homeservice->website }}">{{ $homeservice->website }}</a>
                                                            </li>

                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade show" id="service-d" role="tabpanel"
                                aria-labelledby="service" tabindex="0">
                                <hr class="mb-4">
                                <!-- Service Log Cards -->
                                <ul class="list-group radius-8">
                                    @foreach ($homeservice_services as $homeservice_category)
                                    <li class="list-group-item border text-secondary-light p-16 bg-base border-bottom-0">
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="d-flex">
                                                <iconify-icon icon="mdi:tools" style="font-size: 20px;"></iconify-icon>
                                            </span>
                                            {{ $homeservice_category->service_name }}
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>
                            <div class="tab-pane fade show" id="service-d" role="tabpanel"
                                aria-labelledby="service" tabindex="0">
                                <hr class="mb-4">
                                <!-- Service Log Cards -->
                                <ul class="list-group radius-8">
                                    @foreach ($homeservice_services as $homeservice_category)
                                    <li class="list-group-item border text-secondary-light p-16 bg-base border-bottom-0">
                                        <div class="d-flex align-items-center gap-2">
                                            <span class="d-flex">
                                                <iconify-icon icon="mdi:tools" style="font-size: 20px;"></iconify-icon>
                                            </span>
                                            {{ $homeservice_category->service_name }}
                                        </div>
                                    </li>
                                    @endforeach
                                </ul>
                            </div>

                            <div class="tab-pane fade show" id="images-d" role="tabpanel"
                                aria-labelledby="images" tabindex="0">
                                <div class="row gy-4">
                                    @php
                                        // dd($homeservice->images);
                                    @endphp
                                    {{-- @if(!empty($homeservice->images) && $homeservice->images->count()) --}}
                                    @foreach ($homeservice_images as $image)
                                    <div class="col-xxl-3 col-md-4 col-sm-6">
                                        <div class="hover-scale-img border radius-16 overflow-hidden">
                                            <div class="max-h-266-px overflow-hidden">
                                                <img src="{{ url('storage/' . $image->image_path) }}" alt="" class="hover-scale-img__img w-100 h-100 object-fit-cover">
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                    {{-- @endif --}}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>

@stop

@section('script')
<script>
$(document).ready(function() {
    // Handle verification status toggle
    $(document).on("change", ".verification-toggle", function() {
        let isChecked = $(this).prop("checked"); // Get switch status
        let homeserviceId = $(this).data("id"); // Get Home Service ID
        let verified = isChecked ? 1 : 0; // 1 = Verified, 0 = Not Verified
        let switchElement = $(this); // Store switch reference
        let labelElement = $(this).next("label"); // Get label next to switch

        let actionText = verified === 1 ? "verify" : "unverify";
        let warningText = verified === 1 ?
            "Do you want to verify this home service?" :
            "Do you want to remove verification from this home service?";

        // Revert the toggle until confirmed
        switchElement.prop("checked", !isChecked);

        Swal.fire({
            title: "Are you sure?",
            text: warningText,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#28a745",
            cancelButtonColor: "#d33",
            confirmButtonText: `Yes, ${actionText} it!`,
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                // Disable the switch during the request
                switchElement.prop("disabled", true);

                $.ajax({
                    url: "{{ route('homeservices.update-verification') }}",
                    type: "POST",
                    data: {
                        homeservice_id: homeserviceId,
                        verified: verified,
                        _token: $('meta[name="csrf-token"]').attr("content")
                    },
                    success: function(response) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Update the switch state
                        switchElement.prop("checked", isChecked);

                        // Update text dynamically
                        if (verified === 1) {
                            labelElement.text("Verified");
                            labelElement.removeClass("text-danger").addClass("text-success");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-warning').addClass('bg-success')
                                .html('<iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>Verified')
                                .attr('title', 'Verified Business');
                        } else {
                            labelElement.text("Not Verified");
                            labelElement.removeClass("text-success").addClass("text-danger");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-success').addClass('bg-warning')
                                .html('<iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>Pending')
                                .attr('title', 'Not Verified');
                        }

                        // Show success message
                        Swal.fire({
                            icon: "success",
                            title: "Updated!",
                            text: "Home Service verification status has been changed.",
                            timer: 2000,
                            showConfirmButton: false
                        });
                    },
                    error: function(xhr, status, error) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Show error message
                        let errorMessage = "Failed to update verification status. Please try again.";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: errorMessage,
                            confirmButtonText: "OK"
                        });
                    }
                });
            }
            // If cancelled, the switch remains in its original state (already reverted above)
        });
    });
});
</script>
@endsection
