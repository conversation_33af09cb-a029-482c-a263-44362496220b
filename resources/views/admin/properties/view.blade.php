@extends('admin.layouts.master')
@section('title', 'Clients View- Paidash')
@section('content')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <style>
        .verification-toggle:checked {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
        }
        .verification-toggle:not(:checked) {
            background-color: #dc3545 !important;
            border-color: #dc3545 !important;
        }
        .verification-label {
            font-weight: 600;
            transition: color 0.3s ease;
        }
        .verification-label.text-success {
            color: #28a745 !important;
        }
        .verification-label.text-danger {
            color: #dc3545 !important;
        }
        .form-switch .form-check-input:disabled {
            opadistrict: 0.6;
            cursor: not-allowed;
        }
    </style>
    <div class="dashboard-main-body">

        <div class="d-flex flex-wrap align-items-center justify-content-between gap-3 mb-24">
            <h6 class="fw-semibold mb-0">View Property</h6>
            <ul class="d-flex align-items-center gap-2">
                <li class="fw-medium">
                    <a href="/dashboard" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="solar:home-smile-angle-outline" class="icon text-lg"></iconify-icon>
                        Dashboard
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">
                    <a href="/admin/properties" class="d-flex align-items-center gap-1 hover-text-primary">
                        <iconify-icon icon="flowbite:users-group-outline" class="icon text-lg"></iconify-icon>
                        Property
                    </a>
                </li>
                <li>-</li>
                <li class="fw-medium">View Property</li>
            </ul>
        </div>


        <div class="row gy-4">

            <div class="col-lg-12">
                <div class="card h-100">
                    <div class="card-body p-24">
                        <ul class="w-100 nav border-gradient-tab nav-pills mb-20 d-inline-flex" id="pills-tab"
                            role="tablist">

                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24 active" id="vendor-information"
                                    data-bs-toggle="pill" data-bs-target="#vendor-information-d" type="button" role="tab"
                                    aria-controls="vendor-information-d" aria-selected="false" tabindex="-1">
                                    Property Information
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link d-flex align-items-center px-24" id="images"
                                    data-bs-toggle="pill" data-bs-target="#images-d" type="button"
                                    role="tab" aria-controls="images-d" aria-selected="false"
                                    tabindex="-1">
                                    Images
                                </button>
                            </li>

                        </ul>

                        <div class="tab-content" id="pills-tabContent">

                            <div class="tab-pane fade show active" id="vendor-information-d" role="tabpanel"
                                aria-labelledby="vendor-information" tabindex="0">
                                <div class="user-grid-card position-relative border radius-16 overflow-hidden bg-base h-100">
                                    <img src="./assets/images/user-grid/user-grid-bg1.png" alt="" class="w-100 object-fit-cover">
                                    <div class="pb-24 ms-16 mb-24 me-16 ">
                                        <div class="d-flex align-items-center justify-content-between px-4 py-3 border-bottom">


                                                <div>
                                                    <h5 class="mb-1 d-flex align-items-center flex-wrap">
                                                        <span class="me-2">{{ $property->title }}</span>
                                                        @if($property->verified == 1)
                                                            <span class="badge bg-success d-flex align-items-center" title="Verified Property" id="verificationBadge">
                                                                <iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Verified
                                                            </span>
                                                        @else
                                                            <span class="badge bg-warning d-flex align-items-center" title="Not Verified" id="verificationBadge">
                                                                <iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>
                                                                Not Verified
                                                            </span>
                                                        @endif
                                                    </h5>
                                                </div>


                                        </div>


                                        <div class="row mt-4 gy-4">
                                            <!-- Business Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:office-building" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Contact Information
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            @if($property->builder_id)
                                                            <li><strong>Builder:</strong> <span class="text-muted">{{ $property->builder->name}}</span></li>
                                                            @endif
                                                            <li><strong>Contact Person:</strong> <span class="text-muted">{{ $property->contact_person }}</span></li>
                                                              <li><strong>Mobile:</strong> <span class="text-muted">{{ $property->contact_mobile }}</span></li>
                                                             <li><strong>Email:</strong> <span class="text-muted">{{ $property->contact_email }}</span></li>
                                                            <li class="d-flex align-items-center justify-content-between">
                                                                <strong>Verification Status:</strong>
                                                                <div class="form-switch switch-success d-flex align-items-center">
                                                                    <input class="form-check-input verification-toggle" type="checkbox" role="switch"
                                                                           id="verificationSwitch{{ $property->id }}"
                                                                           data-id="{{ $property->id }}"
                                                                           {{ $property->verified == 1 ? 'checked' : '' }}>
                                                                    <label class="form-check-label ms-2 verification-label {{ $property->verified == 1 ? 'text-success' : 'text-danger' }}" for="verificationSwitch{{ $property->id }}">
                                                                        {{ $property->verified == 1 ? 'Verified' : 'Not Verified' }}
                                                                    </label>
                                                                </div>
                                                            </li>
                                                       </ul>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Contact Information Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:phone" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Property Information
                                                    </div>
                                                @php
                                                    $buildingTypes = [
                                                        1 => 'Apartment',
                                                        2 => 'Independent',
                                                        3 => 'Villa',
                                                        4 => 'Community',
                                                        5 => 'Other'
                                                    ];
                                                @endphp
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Property Type:</strong> {{ $property->property_type == 1 ? 'Sale' : 'Rent' }}</li>

                                                             <li><strong>Building Type:</strong> {{ $property->buildingType->type ?? 'N/A' }}</li>

                                                            <li><strong>Area :</strong> <span class="text-muted">{{ $property->area_size }}{{ $property->areaType->name ?? 'SquareFeet' }}</span></li>
                                                            <li><strong>Build Year:</strong> <span class="text-muted">{{ $property->build_year }}</span></li>
                                                            <li><strong>Facing:</strong> {{ $property->facing_name }}</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:home-map-marker" class="me-2" style="font-size: 20px;"></iconify-icon>
                                                        Aminities
                                                    </div>
                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0 vl-features third color">
                                                            {{-- <li><strong>Car Parking:</strong> <span class="text-muted">{{ $property->amenities_parking == 0 ? 'Yes' : 'No'  }}</span></li>
                                                            <li><strong>Swimming Pool:</strong> <span class="text-muted">{{ $property->amenities_swimming_pool == 0 ? 'Yes' : 'No'  }}</span></li>
                                                           <li><strong>Lift:</strong> <span class="text-muted">{{ $property->amenities_lift == 0 ? 'Yes' : 'No'  }}</span></li>
                                                            <li><strong>Gym:</strong> <span class="text-muted">{{ $property->amenities_gym == 0 ? 'Yes' : 'No'  }}</span></li>
                                                            <li><strong>Spa:</strong> <span class="text-muted">{{ $property->amenities_spa == 0 ? 'Yes' : 'No' }}</span></li> --}}

                                    @if($property->amenities_parking == 1)
                                    <li>Carparking</li>
                                    @endif
                                    @if($property->amenities_swimming_pool == 1)
                                    <li>Swimming Pool</li>
                                    @endif
                                    @if($property->amenities_lift == 1)
                                    <li>Lift</li>
                                    @endif
                                    @if($property->amenities_gym == 1)
                                    <li>Gym</li>
                                    @endif
                                    @if($property->amenities_spa == 1)
                                    <li>Spa</li>
                                    @endif
                                    @if($property->amenities_power_backup == 1)
                                    <li>Power Backup</li>
                                    @endif
                                    @if($property->amenities_water_supply == 1)
                                    <li>24x7 Water Supply</li>
                                    @endif
                                    @if($property->amenities_security == 1)
                                    <li>Security/Watchman</li>
                                    @endif
                                    @if($property->amenities_cctv == 1)
                                    <li>CCTV Surveillance</li>
                                    @endif
                                    @if($property->amenities_clubhouse == 1)
                                    <li>Clubhouse</li>
                                    @endif
                                    @if($property->amenities_indoor_games == 1)
                                    <li>Indoor Games Room</li>
                                    @endif
                                    @if($property->amenities_outdoor_games == 1)
                                    <li>Outdoor Sports</li>
                                    @endif
                                    @if($property->amenities_kids_play == 1)
                                    <li>Kids Play Area</li>
                                    @endif

                                                        </ul>

                                                        @if($property->other_amenities)
                                                        <div class="mt-3">
                                                            <strong>Other Amenities:</strong>
                                                            <p class="text-muted mt-2 mb-0">{{ $property->other_amenities }}</p>
                                                        </div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Additional Details Card -->
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:share-variant" class="me-2" style="font-size: 22px;"></iconify-icon>
                                                         Features
                                                    </div>

                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            <li><strong>Bedrooms:</strong> {{ $property->no_of_bed_rooms }} Beds</li>
                                                            <li><strong>Bathrooms:</strong> {{ $property->no_of_bath_rooms }} Bath</li>
                                                            <li><strong>Balconies:</strong> {{ $property->no_of_balconies }}</li>
                                                            <li><strong>Car Parkings:</strong> {{ $property->no_of_car_parkings }}</li>
                                                            <li><strong>Floors:</strong> {{ $property->no_of_floors }}</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:cash" class="me-2" style="font-size: 22px;"></iconify-icon>
                                                         Costing
                                                    </div>

                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                            @if($property->property_type == 1)
                                                               <li><strong>Selling Price:</strong> <span class="text-muted">₹{{ number_format($property->property_cost, 2) }}</span></li>
                                                            @else
                                                                <li><strong>Advance Amount:</strong> <span class="text-muted">₹{{ number_format($property->advance_amount, 2) }}</span></li>
                                                                <li><strong>Monthly Rent:</strong> <span class="text-muted">₹{{ number_format($property->rent, 2) }}</span></li>
                                                            @endif
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card shadow-sm border-0 rounded-3">
                                                    <div class="card-header bg-primary-400 text-white fw-bold d-flex align-items-center">
                                                        <iconify-icon icon="mdi:share-variant" class="me-2" style="font-size: 22px;"></iconify-icon>
                                                         Location
                                                    </div>

                                                    <div class="card-body bg-light">
                                                        <ul class="list-unstyled lh-lg mb-0">
                                                              <li><strong>Address:</strong> <span class="text-muted">{{ $property->address }}</span></li>

                                                            <li><strong>Area:</strong> <span class="text-muted">{{ $property->area->name }}{{ $property->area->pincode ? ' : ' . $property->area->pincode : '' }}</span></li>
                                                           <li><strong>District:</strong> <span class="text-muted">{{ $property->district->name }}</span></li>
                                                            <li><strong>State:</strong> <span class="text-muted">{{ $property->state->name }}</span></li>
                                                            @if($property->nearest_landmark)
                                                            <li><strong>Nearest Landmark:</strong> <span class="text-muted">{{ $property->nearest_landmark }}</span></li>
                                                            @endif
                                                            @if($property->distance)
                                                            <li><strong>Distance:</strong> <span class="text-muted">{{ $property->distance }}</span></li>
                                                            @endif


                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>


                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade show" id="images-d" role="tabpanel"
                                aria-labelledby="images" tabindex="0">
                                <div class="row gy-4">
                                    @foreach ($property_images as $image)
                                    <div class="col-xxl-3 col-md-4 col-sm-6">
                                        <div class="hover-scale-img border radius-16 overflow-hidden">
                                            <div class="max-h-266-px overflow-hidden">
                                                <img src="{{ url('storage/' . $image->image_path) }}" alt="" class="hover-scale-img__img w-100 h-100 object-fit-cover">
                                            </div>
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>





                        </div>
                    </div>
                </div>
            </div>
        </div>


    </div>



@stop

@section('script')
<script>
$(document).ready(function() {
    // Handle verification status toggle
    $(document).on("change", ".verification-toggle", function() {
        let isChecked = $(this).prop("checked"); // Get switch status
        let propertyId = $(this).data("id"); // Get Property ID
        let verified = isChecked ? 1 : 0; // 1 = Verified, 0 = Not Verified
        let switchElement = $(this); // Store switch reference
        let labelElement = $(this).next("label"); // Get label next to switch

        let actionText = verified === 1 ? "verify" : "unverify";
        let warningText = verified === 1 ?
            "Do you want to verify this property?" :
            "Do you want to remove verification from this property?";

        // Revert the toggle until confirmed
        switchElement.prop("checked", !isChecked);

        Swal.fire({
            title: "Are you sure?",
            text: warningText,
            icon: "warning",
            showCancelButton: true,
            confirmButtonColor: "#28a745",
            cancelButtonColor: "#d33",
            confirmButtonText: `Yes, ${actionText} it!`,
            cancelButtonText: "Cancel"
        }).then((result) => {
            if (result.isConfirmed) {
                // Disable the switch during the request
                switchElement.prop("disabled", true);

                $.ajax({
                    url: "{{ route('properties.update-verification') }}",
                    type: "POST",
                    data: {
                        property_id: propertyId,
                        verified: verified,
                        _token: $('meta[name="csrf-token"]').attr("content")
                    },
                    success: function(response) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Update the switch state
                        switchElement.prop("checked", isChecked);

                        // Update text dynamically
                        if (verified === 1) {
                            labelElement.text("Verified");
                            labelElement.removeClass("text-danger").addClass("text-success");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-warning').addClass('bg-success')
                                .html('<iconify-icon icon="mdi:check-circle" class="me-1" style="font-size: 12px;"></iconify-icon>Verified')
                                .attr('title', 'Verified Property');
                        } else {
                            labelElement.text("Not Verified");
                            labelElement.removeClass("text-success").addClass("text-danger");
                            // Update badge in header
                            $('#verificationBadge').removeClass('bg-success').addClass('bg-warning')
                                .html('<iconify-icon icon="mdi:clock-outline" class="me-1" style="font-size: 12px;"></iconify-icon>Not Verified')
                                .attr('title', 'Not Verified');
                        }

                        // Show success message
                        Swal.fire({
                            icon: "success",
                            title: "Updated!",
                            text: "Property verification status has been changed.",
                            timer: 2000,
                            showConfirmButton: false
                        });
                    },
                    error: function(xhr, status, error) {
                        // Enable the switch again
                        switchElement.prop("disabled", false);

                        // Show error message
                        let errorMessage = "Failed to update verification status. Please try again.";
                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        Swal.fire({
                            icon: "error",
                            title: "Error!",
                            text: errorMessage,
                            confirmButtonText: "OK"
                        });
                    }
                });
            }
            // If cancelled, the switch remains in its original state (already reverted above)
        });
    });
});
</script>
@endsection
